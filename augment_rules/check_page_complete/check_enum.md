# 页面枚举修复规则

## 任务概述
已知 `all_constant.md` 中包含系统中所有需要的枚举值，`globalConstantsManager.js` 是管理这些枚举值的工具类。
我需要你根据给定的页面路由（例如：`system-settings/system-config/system-settings`）和指定的参数，根据页面的业务逻辑，从 `all_constant.md` 文件中找到适合的枚举值，如果找到请使用 `globalConstantsManager.js` 管理的枚举值来替换和指定的参数相关的内容，如果没有适合的，不需要强行替换，保持原样即可。

## 重要传值规则
**⚠️ 关键规则：枚举传值方式**
- **Locale.Language 枚举**：显示和传值都使用 `key` 字段
- **其他所有枚举**：显示使用 `name` 字段，传值使用 `key` 字段（不是 `id`）

**⚠️ 特别注意：globalConstantsManager 已经内置了 Locale.Language 的特殊处理**
- `getEnumName('Locale.Language', value)` 会自动返回 `key` 字段而不是 `name` 字段
- `getSelectOptionsByKey('Locale.Language')` 会自动使用 `key` 作为 `label` 而不是 `name`
- 因此在使用这些方法时，无需手动处理 Locale.Language 的特殊情况

## 必须遵守的规则
不论什么情况，都不允许修改枚举值，任何一点修改都不允许

## 执行步骤

### 1. 分析页面业务逻辑
- 根据给定的页面路由找到对应的React组件文件
- 分析页面中使用的硬编码枚举值、下拉选项、状态显示等
- 识别需要替换为全局枚举的字段和参数

### 2. 自动处理流程
**处理原则**：
- **100%匹配**：立即执行代码替换
- **非100%匹配**：记录到 `check_enum_record.md` 文件
- **未找到匹配**：记录到 `check_enum_record.md` 文件
- **无需用户确认**：整个过程自动化执行

### 3. 查找匹配的枚举配置

#### 3.1 搜索策略
按以下优先级在 `all_constant.md` 文件中搜索匹配的枚举配置：

1. **精确字段名匹配**：
   - 搜索包含目标字段名的枚举键（如字段 `status` 搜索 `*.Status`）
   - 搜索包含字段语义的枚举键（如字段 `verified` 搜索 `*.Verified`）

2. **业务模块匹配**：
   - 根据页面路由推断业务模块（如 `user-management` → `User.*`）
   - 根据页面功能推断模块（如订单页面 → `Order.*`）

3. **数据值匹配**：
   - 分析页面中硬编码的选项值和文本
   - 在枚举配置中查找相同或相似的 `id`、`key`、`name` 值

#### 3.2 具体匹配流程

**步骤1：提取页面中的硬编码枚举信息**
```javascript
// 示例：从页面代码中识别这样的硬编码
<Select>
  <Option value={0}>未激活</Option>
  <Option value={1}>已激活</Option>
  <Option value={2}>已禁用</Option>
</Select>

// 或者这样的状态映射
const statusMap = {
  0: '待审核',
  1: '已通过',
  2: '已拒绝'
};
```

**步骤2：在 all_constant.md 中搜索匹配项**
使用以下关键词组合搜索：
- 字段名 + 常见后缀（如 `status`、`verified`、`type`、`level`）
- 页面模块名（如 `User`、`Order`、`System`）
- 硬编码的显示文本（如 `已激活`、`未激活`）

**步骤3：评估匹配度**
对找到的枚举配置进行匹配度评分：
- **高匹配度（90-100%）**：字段名完全对应，且枚举值和显示文本高度一致
- **中匹配度（70-89%）**：字段语义相近，大部分枚举值匹配
- **低匹配度（50-69%）**：业务领域相关，但具体值可能需要调整

#### 3.3 匹配示例

**示例1：用户状态字段**
```javascript
// 页面中的硬编码
<Select>
  <Option value={0}>未验证</Option>
  <Option value={1}>已验证</Option>
</Select>

// 在 all_constant.md 中找到匹配项
"User.Verified": [
  { id: 0, key: "UNVERIFIED", name: "未验证" },
  { id: 1, key: "VERIFIED", name: "已验证" }
]
// 匹配度：100% - 完全匹配
```

**示例2：订单状态字段**
```javascript
// 页面中的硬编码
const orderStatusMap = {
  'pending': '待处理',
  'completed': '已完成',
  'cancelled': '已取消'
};

// 在 all_constant.md 中找到匹配项
"Order.Status": [
  { id: 0, key: "PENDING", name: "待处理" },
  { id: 1, key: "COMPLETED", name: "已完成" },
  { id: 2, key: "CANCELLED", name: "已取消" }
]
// 匹配度：95% - key格式略有差异但语义完全一致
// 注意：传值时使用 key 值（"PENDING", "COMPLETED", "CANCELLED"），显示使用 name
```

#### 3.4 匹配结果处理

**当找到100%匹配度的枚举时**：
- 直接使用该枚举进行代码替换
- 无需用户确认，立即执行步骤4的代码替换
- 输出替换成功的信息

**当找到非100%匹配度的枚举时**：
- 将页面路径和枚举信息记录到 `check_enum_record.md` 文件中
- 记录格式：
  ```markdown
  ## [页面路径]
  - **字段名**: [字段名称]
  - **找到的枚举**: [枚举名称]
  - **匹配度**: [匹配度百分比]
  - **差异说明**: [具体差异描述]
  - **建议**: [是否建议使用此枚举]
  - **时间**: [记录时间]

  ---
  ```
- 跳过此字段的处理，继续处理下一个字段

**当未找到匹配项时**：
- 将页面路径和字段信息记录到 `check_enum_record.md` 文件中
- 记录格式：
  ```markdown
  ## [页面路径]
  - **字段名**: [字段名称]
  - **硬编码内容**: [当前硬编码的选项或映射]
  - **搜索结果**: 未找到匹配的枚举配置
  - **建议**: 保持现有硬编码或考虑创建新枚举
  - **时间**: [记录时间]

  ---
  ```

#### 3.5 记录文件管理

**check_enum_record.md 文件结构**：
```markdown
# 枚举匹配记录

本文件记录了在页面枚举修复过程中发现的非100%匹配枚举和未找到匹配项的情况。

## 记录说明
- **100%匹配的枚举**：直接进行代码替换，不记录在此文件中
- **非100%匹配的枚举**：记录详细信息供后续人工审核
- **未找到匹配项**：记录字段信息供后续创建新枚举参考

---

[具体记录内容]
```

**记录操作规则**：
1. 每次处理页面时，先检查 `check_enum_record.md` 文件是否存在
2. 如果不存在，创建新文件并添加文件头部说明
3. 如果存在，在文件末尾追加新的记录
4. 每条记录之间用 `---` 分隔线分隔
5. 记录时间使用 ISO 8601 格式（YYYY-MM-DD HH:mm:ss）

### 4. 使用 useGlobalConstants Hook 替换硬编码值

#### 4.1 导入必要的Hook
```javascript
import { useGlobalConstants } from '@/hooks/useGlobalConstants';
```

#### 4.2 在组件中获取枚举数据
```javascript
function YourComponent() {
  const {
    getSelectOptions,
    getEnumName,
    getEnumKey,
    loading
  } = useGlobalConstants();

  // 获取下拉选项（注意：所有枚举传值使用 key）
  const statusOptions = getSelectOptions('YourModule.Status').map(option => ({
    label: option.name,  // 显示使用 name
    value: option.key    // 传值使用 key
  }));

  // 获取显示名称
  const statusName = getEnumName('YourModule.Status', record.status);

  // 获取枚举的 key 值（用于传参）
  const statusKey = getEnumKey('YourModule.Status', record.status);
}
```

#### 4.3 替换表单中的硬编码选项
**修复前**：
```javascript
<Select placeholder="选择状态">
  <Option value={0}>未激活</Option>
  <Option value={1}>已激活</Option>
  <Option value={2}>已禁用</Option>
</Select>
```

**修复后**：
```javascript
<Select
  placeholder="选择状态"
  options={getSelectOptions('YourModule.Status').map(option => ({
    label: option.name,  // 显示使用 name
    value: option.key    // 传值使用 key（重要：不是 id）
  }))}
  loading={loading}
/>
```

#### 4.4 替换表格中的状态显示
**修复前**：
```javascript
{
  title: '状态',
  dataIndex: 'status',
  render: (value) => {
    const statusMap = {
      0: '未激活',
      1: '已激活',
      2: '已禁用'
    };
    return statusMap[value] || '未知';
  }
}
```

**修复后**：
```javascript
{
  title: '状态',
  dataIndex: 'status',
  render: (value) => {
    const statusName = getEnumName('YourModule.Status', value);
    return statusName || '未知';
  }
}
```

#### 4.5 枚举传值规则

**⚠️ 重要规则：所有枚举的传值规则**：

1. **Locale.Language 枚举特殊规则**：
   - **显示规则**：不使用 `name` 字段，直接使用 `key` 字段进行显示
   - **传参规则**：传递参数时也使用 `key` 字段的值

2. **其他所有枚举的标准规则**：
   - **显示规则**：使用 `name` 字段进行显示
   - **传参规则**：传递参数时使用 `key` 字段的值（而不是 `id`）

**Locale.Language 枚举处理示例**：
```javascript
{
  title: '语言',
  dataIndex: 'language',
  render: (value) => {
    // 对于 Locale.Language，getEnumName 会自动返回 key 字段
    const languageName = getEnumName('Locale.Language', value);
    return languageName || '未知';
  }
}

// 表单选项处理 - 推荐使用 getSelectOptionsByKey
<Select
  placeholder="选择语言"
  options={getSelectOptionsByKey('Locale.Language')}  // 自动处理 Locale.Language 的特殊显示
  loading={loading}
/>

// 或者使用 getSelectOptions（需要手动映射）
<Select
  placeholder="选择语言"
  options={getSelectOptions('Locale.Language').map(option => ({
    label: option.key,  // 手动使用 key 作为显示文本
    value: option.key   // 传值也用 key
  }))}
  loading={loading}
/>
```

**其他枚举处理示例**：
```javascript
{
  title: '状态',
  dataIndex: 'status',
  render: (value) => {
    // 其他枚举使用 name 进行显示
    const statusName = getEnumName('YourModule.Status', value);
    return statusName || '未知';
  }
}

// 表单选项处理 - 传值使用 key
<Select
  placeholder="选择状态"
  options={getSelectOptions('YourModule.Status').map(option => ({
    label: option.name,  // 显示使用 name
    value: option.key    // 传值使用 key（重要：不是 id）
  }))}
  loading={loading}
/>
```

**注意**：
- `Locale.Language` 枚举：显示和传值都使用 `key`
- 其他所有枚举：显示使用 `name`，传值使用 `key`

#### 4.6 getSelectOptions vs getSelectOptionsByKey 使用规则

**⚠️ 重要：选择正确的方法获取枚举选项**

1. **getSelectOptions(enumKey)** - 返回格式：`[{id, key, name}, ...]`
   - 用于需要完整枚举信息的场景
   - 过滤时使用 `option.key` 进行匹配
   - 映射时根据枚举类型选择合适的字段

2. **getSelectOptionsByKey(enumKey)** - 返回格式：`[{label, value}, ...]`
   - 用于直接作为Select组件options的场景
   - 过滤时使用 `option.value` 进行匹配
   - 已经预处理好了label和value字段

**⚠️ 特别注意：Locale.Language 枚举的自动处理**

由于 `globalConstantsManager` 已经内置了 `Locale.Language` 的特殊处理，因此：

**推荐使用方式**：
```javascript
// ✅ 推荐：直接使用 getSelectOptionsByKey，自动处理显示逻辑
getSelectOptionsByKey('Locale.Language')
  .filter(option => ['zh_CN', 'en_US'].includes(option.value))

// ✅ 推荐：直接使用 getEnumName，自动返回 key 字段
const languageName = getEnumName('Locale.Language', value);
```

**手动处理方式（不推荐，但仍然有效）**：
```javascript
// ✅ 正确：使用 getSelectOptions 时需要手动映射
getSelectOptions('Locale.Language')
  .filter(option => ['zh_CN', 'en_US'].includes(option.key))
  .map(option => ({
    label: option.key,  // 手动使用 key 作为显示文本
    value: option.key   // 传值也用 key
  }))
```

**常见错误示例**：
```javascript
// ❌ 错误：使用 getSelectOptions 但过滤时用 option.value
getSelectOptions('Locale.Language')
  .filter(option => ['zh_CN', 'en_US'].includes(option.value)) // 错误！应该用 option.key
```

**其他枚举的标准处理**：
```javascript
// 其他枚举使用标准方式，无需特殊处理
getSelectOptions('User.Status')
  .filter(option => ['ACTIVE', 'INACTIVE'].includes(option.key))
  .map(option => ({
    label: option.name,  // 标准：显示用 name
    value: option.key    // 标准：传值用 key
  }))
```

**选择建议**：
- **所有枚举（包括 Locale.Language）**：优先使用 `getSelectOptionsByKey`，它会自动处理显示逻辑
- **Locale.Language 枚举**：`getSelectOptionsByKey` 和 `getEnumName` 已自动处理特殊显示需求
- **其他所有枚举**：按标准规则处理，显示用 name，传值用 key
- 如果只需要作为Select的options，优先使用 `getSelectOptionsByKey`
- 如果需要访问完整的枚举信息（如id、key、name），使用 `getSelectOptions`

#### 4.7 替换搜索条件中的选项
**修复前**：
```javascript
<Form.Item label="状态筛选" name="status">
  <Select allowClear>
    <Option value="">全部</Option>
    <Option value={0}>未激活</Option>
    <Option value={1}>已激活</Option>
  </Select>
</Form.Item>
```

**修复后**：
```javascript
<Form.Item label="状态筛选" name="status">
  <Select
    allowClear
    placeholder="选择状态"
    options={[
      { label: '全部', value: '' },
      ...getSelectOptions('YourModule.Status').map(option => ({
        label: option.name,  // 显示使用 name
        value: option.key    // 传值使用 key
      }))
    ]}
    loading={loading}
  />
</Form.Item>
```

### 5. 常见枚举类型匹配规则

#### 5.1 用户相关枚举
- `User.Verified` - 用户验证状态（已验证/未验证）
- `User.Status` - 用户状态（激活/禁用等）
- `User.Role` - 用户角色
- `User.Gender` - 用户性别

#### 5.2 订单相关枚举
- `Order.Status` - 订单状态（待处理/已完成等）
- `Order.PaymentStatus` - 支付状态
- `Order.DeliveryStatus` - 配送状态

#### 5.3 系统配置相关枚举
- `System.Status` - 系统状态
- `System.ConfigType` - 配置类型
- `System.LogLevel` - 日志级别

#### 5.4 通用状态枚举
- `Common.Status` - 通用状态（启用/禁用）
- `Common.YesNo` - 是否选择
- `Common.Priority` - 优先级

### 6. 修复验证

#### 6.1 功能验证
- 确保下拉选项正确显示
- 验证选择值能正确保存和回显
- 检查表格中状态显示是否正确

#### 6.2 性能验证
- 确保枚举数据正确缓存
- 验证页面加载时有适当的loading状态
- 检查是否有不必要的重复请求

#### 6.3 错误处理
- 验证枚举数据加载失败时的降级处理
- 确保未知枚举值有合适的默认显示

## 注意事项

1. **保持向后兼容**：替换时确保现有功能不受影响
2. **合理匹配**：选择最符合业务语义的枚举类型
3. **性能考虑**：利用枚举数据的缓存机制，避免重复请求
4. **用户体验**：在枚举数据加载时提供适当的loading状态
5. **错误处理**：为枚举数据加载失败提供降级方案

## 实施原则

- **优先使用全局枚举**：能用全局枚举的地方不要使用硬编码
- **语义匹配**：选择语义最接近的枚举类型
- **统一性**：同一类型的字段在不同页面使用相同的枚举配置
- **可维护性**：通过全局枚举管理，便于后续维护和扩展
- **自动化处理**：100%匹配的枚举直接替换，非100%匹配的记录到文件中

## 处理流程总结

1. **分析页面**：识别硬编码枚举值
2. **自动匹配**：在 `all_constant.md` 中查找匹配的枚举
3. **自动处理**：
   - 100%匹配 → 立即替换代码
   - 非100%匹配 → 记录到 `augment_rules/check_page_complete/check_enum_record.md`
   - 未找到匹配 → 记录到 `augment_rules/check_page_complete/check_enum_record.md`
4. **验证结果**：确保替换后的代码正常工作

**重要提醒**：整个过程无需用户确认，完全自动化执行。只有100%匹配的枚举才会被自动替换，确保代码安全性。