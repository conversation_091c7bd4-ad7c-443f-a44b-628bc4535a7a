import { useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  initGlobalConstants,
  fetchGlobalConstants,
  refreshGlobalConstants,
  clearGlobalConstantsCache
} from '@/redux/slices/globalConstantsSlice';
import globalConstantsManager from '@/utils/globalConstantsManager';

/**
 * 全局枚举配置 Hook
 * 提供便捷的全局枚举配置访问和操作方法
 */
export const useGlobalConstants = () => {
  const dispatch = useDispatch();
  const { constants, loading, error, initialized, lastUpdated } = useSelector(
    state => state.globalConstants
  );

  // 初始化全局枚举配置
  const initConstants = useCallback(() => {
    dispatch(initGlobalConstants());
  }, [dispatch]);

  // 获取最新的全局枚举配置
  const fetchConstants = useCallback(() => {
    dispatch(fetchGlobalConstants());
  }, [dispatch]);

  // 刷新全局枚举配置
  const refreshConstants = useCallback(() => {
    dispatch(refreshGlobalConstants());
  }, [dispatch]);

  // 清除缓存
  const clearCache = useCallback(() => {
    dispatch(clearGlobalConstantsCache());
  }, [dispatch]);

  // 获取指定枚举的所有选项
  const getEnumOptions = useCallback((enumKey) => {
    return globalConstantsManager.getEnumOptions(enumKey);
  }, [constants]);

  // 根据 id 获取枚举项
  const getEnumById = useCallback((enumKey, id) => {
    return globalConstantsManager.getEnumById(enumKey, id);
  }, [constants]);

  // 根据 key 获取枚举项
  const getEnumByKey = useCallback((enumKey, key) => {
    return globalConstantsManager.getEnumByKey(enumKey, key);
  }, [constants]);

  // 获取枚举项的显示名称
  const getEnumName = useCallback((enumKey, idOrKey) => {
    return globalConstantsManager.getEnumName(enumKey, idOrKey);
  }, [constants]);

  // 获取枚举项的 key 值
  const getEnumKey = useCallback((enumKey, idOrKey) => {
    return globalConstantsManager.getEnumKey(enumKey, idOrKey);
  }, [constants]);

  // 检查枚举值是否存在
  const hasEnum = useCallback((enumKey, idOrKey) => {
    return globalConstantsManager.hasEnum(enumKey, idOrKey);
  }, [constants]);

  // 获取枚举的键值对映射 (key -> name)
  const getEnumMap = useCallback((enumKey) => {
    return globalConstantsManager.getEnumMap(enumKey);
  }, [constants]);

  // 获取枚举的 ID 值对映射 (id -> name)
  const getEnumIdMap = useCallback((enumKey) => {
    return globalConstantsManager.getEnumIdMap(enumKey);
  }, [constants]);

  // 获取适用于 Ant Design Select 的选项 (使用 id 作为 value)
  const getSelectOptions = useCallback((enumKey) => {
    return globalConstantsManager.getSelectOptions(enumKey);
  }, [constants]);

  // 获取适用于 Ant Design Select 的选项 (使用 key 作为 value)
  const getSelectOptionsByKey = useCallback((enumKey) => {
    return globalConstantsManager.getSelectOptionsByKey(enumKey);
  }, [constants]);

  // 获取所有枚举配置
  const getAllConstants = useCallback(() => {
    return globalConstantsManager.getAllConstants();
  }, [constants]);

  // 获取所有枚举的键名
  const getEnumKeys = useCallback(() => {
    return globalConstantsManager.getEnumKeys();
  }, [constants]);

  // 自动初始化（仅在未初始化时）
  useEffect(() => {
    if (!initialized) {
      initConstants();
    }
  }, [initialized, initConstants]);

  return {
    // 状态
    constants,
    loading,
    error,
    initialized,
    lastUpdated,

    // 操作方法
    initConstants,
    fetchConstants,
    refreshConstants,
    clearCache,

    // 查询方法
    getEnumOptions,
    getEnumById,
    getEnumByKey,
    getEnumName,
    getEnumKey,
    hasEnum,
    getEnumMap,
    getEnumIdMap,
    getSelectOptions,
    getSelectOptionsByKey,
    getAllConstants,
    getEnumKeys
  };
};
