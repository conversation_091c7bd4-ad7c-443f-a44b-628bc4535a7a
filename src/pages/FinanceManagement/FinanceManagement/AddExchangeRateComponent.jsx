import React, { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import {
  Card,
  Form,
  Input,
  Button,
  Space,
  Typography,
  message,
  Select,
  InputNumber,
} from "antd";
import { SaveOutlined, UndoOutlined } from "@ant-design/icons";
import {
  addExchangeRate,
  updateExchangeRate,
  batchUpdateCurrency,
  getExchangeRateDetail,
} from "@/redux/exchangeRateConfigPage/exchangeRateConfigPageSlice";

const { Title } = Typography;
const { Option } = Select;

function AddExchangeRateComponent({
  editingRecord,
  selectedRowKeys = [],
  onSave,
  onCancel,
  tabType,
  tabKey,
  saveTabFormData,
  getTabFormData,
  clearTabFormData,
}) {
  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (editingRecord && tabType === "edit") {
      // 编辑模式：获取详细信息并填充表单
      const fetchDetail = async () => {
        try {
          const response = await dispatch(
            getExchangeRateDetail(editingRecord.id)
          ).unwrap();
          form.setFieldsValue({
            country: response.country,
            currency: response.currency,
            currencyName: response.currencyName,
            symbolFloat: response.symbolFloat,
            currencySymbol: response.currencySymbol,
            rate: response.rate,
            mark: response.mark,
          });
        } catch (error) {
          message.error(error || "获取汇率配置详情失败");
        }
      };
      fetchDetail();
    } else {
      // 添加模式：尝试恢复之前保存的表单数据
      if (tabKey && getTabFormData) {
        const savedFormData = getTabFormData(tabKey);
        if (savedFormData) {
          form.setFieldsValue(savedFormData);
        } else {
          form.resetFields();
        }
      } else {
        form.resetFields();
      }
    }
  }, [editingRecord, form, tabType, dispatch, tabKey, getTabFormData]);

  const handleFormSubmit = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();

      if (tabType === "batchUpdate") {
        // 批量更新汇率
        await dispatch(
          batchUpdateCurrency({
            ids: selectedRowKeys,
            updateDTO: values,
          })
        ).unwrap();
        message.success(
          `已修改 ${selectedRowKeys.length} 个汇率配置的货币代码`
        );
      } else if (editingRecord && tabType === "edit") {
        // 更新汇率配置
        await dispatch(
          updateExchangeRate({
            id: editingRecord.id,
            requestData: values,
          })
        ).unwrap();
        message.success("更新汇率配置成功");
      } else {
        // 添加汇率配置
        await dispatch(addExchangeRate(values)).unwrap();
        message.success("添加汇率配置成功");
        form.resetFields();
      }

      // 清除表单数据（仅在添加模式下）
      if (!editingRecord && tabKey && clearTabFormData) {
        clearTabFormData(tabKey);
      }
      onSave && onSave();
    } catch (error) {
      console.error("操作失败:", error);
      if (tabType === "batchUpdate") {
        message.error("批量修改货币代码失败");
      } else {
        message.error(editingRecord ? "更新失败" : "添加失败");
      }
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    if (tabKey && clearTabFormData) {
      clearTabFormData(tabKey);
    }
    onCancel && onCancel();
  };

  const getTitle = () => {
    if (tabType === "batchUpdate") {
      return "批量修改货币";
    }
    return editingRecord ? "编辑汇率配置" : "添加汇率配置";
  };

  const renderBatchUpdateForm = () => (
    <Form
      form={form}
      layout="vertical"
      style={{ maxWidth: 600 }}
      onValuesChange={() => {
        // 实时保存表单数据（仅在添加模式下）
        if (!editingRecord && tabKey && saveTabFormData) {
          const formData = form.getFieldsValue();
          const hasData = Object.values(formData).some(
            (value) => value && value.toString().trim() !== ""
          );
          if (hasData) {
            saveTabFormData(tabKey, formData);
          }
        }
      }}
    >
      <Form.Item
        label="新货币代码"
        name="currency"
        rules={[
          { required: true, message: "请输入货币代码" },
          { pattern: /^[A-Z]{3}$/, message: "货币代码必须是3位大写字母" },
        ]}
      >
        <Input
          style={{ width: "100%" }}
          placeholder="请输入货币代码（如：USD、EUR、CNY）"
          maxLength={3}
          onChange={(e) => {
            e.target.value = e.target.value.toUpperCase();
          }}
        />
      </Form.Item>
      <Form.Item>
        <Space>
          <Button
            type="primary"
            onClick={handleFormSubmit}
            icon={<SaveOutlined />}
            loading={loading}
          >
            保存
          </Button>
          <Button
            onClick={handleCancel}
            icon={<UndoOutlined />}
            disabled={loading}
          >
            取消
          </Button>
        </Space>
      </Form.Item>
    </Form>
  );

  const renderAddEditForm = () => (
    <Form
      form={form}
      layout="vertical"
      style={{ maxWidth: 600 }}
      onValuesChange={() => {
        // 实时保存表单数据（仅在添加模式下）
        if (!editingRecord && tabKey && saveTabFormData) {
          const formData = form.getFieldsValue();
          const hasData = Object.values(formData).some(
            (value) => value && value.toString().trim() !== ""
          );
          if (hasData) {
            saveTabFormData(tabKey, formData);
          }
        }
      }}
    >
      <Form.Item
        label="国家"
        name="country"
        rules={[{ required: true, message: "请输入国家" }]}
      >
        <Input placeholder="请输入国家" />
      </Form.Item>

      <Form.Item
        label="货币代码"
        name="currency"
        rules={[{ required: true, message: "请输入货币代码" }]}
      >
        <Input placeholder="请输入货币代码" />
      </Form.Item>

      <Form.Item
        label="货币名称"
        name="currencyName"
        rules={[{ required: true, message: "请输入货币名称" }]}
      >
        <Input placeholder="请输入货币名称" />
      </Form.Item>

      <Form.Item
        label="符号位置"
        name="symbolFloat"
        rules={[{ required: true, message: "请选择符号位置" }]}
      >
        <Select placeholder="请选择符号位置">
          <Option value={1}>量左</Option>
          <Option value={2}>量右</Option>
        </Select>
      </Form.Item>

      <Form.Item
        label="货币号码"
        name="currencySymbol"
        rules={[{ required: true, message: "请输入货币号码" }]}
      >
        <Input placeholder="请输入货币号码" />
      </Form.Item>

      <Form.Item
        label="汇率"
        name="rate"
        rules={[
          { required: true, message: "请输入汇率" },
          { type: "number", min: 0, message: "汇率必须大于等于0" },
        ]}
      >
        <InputNumber
          style={{ width: "100%" }}
          placeholder="请输入汇率"
          precision={4}
        />
      </Form.Item>

      <Form.Item label="备注" name="mark">
        <Input.TextArea placeholder="请输入备注" rows={3} />
      </Form.Item>

      <Form.Item>
        <Space>
          <Button
            type="primary"
            onClick={handleFormSubmit}
            icon={<SaveOutlined />}
            loading={loading}
          >
            保存
          </Button>
          <Button
            onClick={handleCancel}
            icon={<UndoOutlined />}
            disabled={loading}
          >
            取消
          </Button>
        </Space>
      </Form.Item>
    </Form>
  );

  return (
    <div>
      <Title level={3}>{getTitle()}</Title>
      <Card>
        {tabType === "batchUpdate"
          ? renderBatchUpdateForm()
          : renderAddEditForm()}
      </Card>
    </div>
  );
}

export default AddExchangeRateComponent;
