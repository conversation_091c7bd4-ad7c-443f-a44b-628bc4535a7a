import React, { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import {
  Card,
  Form,
  Input,
  Button,
  Space,
  Typography,
  message,
  InputNumber,
  Row,
  Col,
  DatePicker,
} from "antd";
import { SaveOutlined, UndoOutlined } from "@ant-design/icons";
import dayjs from "dayjs";
import {
  addRechargeOrder,
  updateRechargeOrder,
  fetchRechargeOrderDetail,
  clearSelectedRecord,
} from "@/redux/rechargeOrderPage/rechargeOrderPageSlice";

const { Title } = Typography;

function AddRechargeOrderComponent({
  editingRecord,
  onSave,
  onCancel,
  tabKey,
  saveTabFormData,
  getTabFormData,
  clearTabFormData,
}) {
  console.log("AddRechargeOrderComponent 渲染:", {
    editingRecord,
    tabKey,
    hasEditingRecord: !!editingRecord,
    editingRecordId: editingRecord?.id,
  });
  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    console.log("AddRechargeOrderComponent useEffect 触发:", {
      editingRecord,
      tabKey,
    });

    if (editingRecord) {
      console.log("进入编辑模式，editingRecord:", editingRecord);
      // 编辑模式：获取详细信息并填充表单
      const fetchDetail = async () => {
        try {
          const response = await dispatch(
            fetchRechargeOrderDetail(editingRecord.id)
          ).unwrap();

          // 处理响应数据结构
          const detail = response?.data || response;
          console.log("获取到的详情数据:", detail);

          // 填充表单数据
          // 填充表单数据，处理可能缺失的字段
          const formData = {
            userId: detail.user?.id || detail.userId,
            paymentId: detail.payment?.id || detail.paymentId,
            transactionId: detail.transactionId || "",
            amount: detail.amount || 0,
            currency: detail.currency || "",
            paymentTime: detail.paymentTime
              ? dayjs.unix(detail.paymentTime)
              : null,
            refundAmount: detail.refundAmount || 0,
            refundTime: detail.refundTime
              ? dayjs.unix(detail.refundTime)
              : null,
            paymentMethod: detail.paymentMethod || "",
            ip: detail.ip || "",
            mark: detail.mark || "",
            extraData: detail.extraData || "",
          };

          console.log("设置表单数据:", formData);
          form.setFieldsValue(formData);

          // 验证表单数据是否正确设置
          setTimeout(() => {
            const currentValues = form.getFieldsValue();
            console.log("表单当前值:", currentValues);
          }, 100);
        } catch (error) {
          console.error("获取详情失败:", error);
          message.error(error || "获取详情失败");
        }
      };
      fetchDetail();
    } else {
      // 添加模式：尝试恢复之前保存的表单数据
      if (tabKey && getTabFormData) {
        const savedFormData = getTabFormData(tabKey);
        console.log(`[${tabKey}] 恢复表单数据:`, savedFormData);
        if (savedFormData) {
          form.setFieldsValue(savedFormData);
          console.log(`[${tabKey}] 表单数据已恢复`);
        } else {
          form.resetFields();
          console.log(`[${tabKey}] 没有保存的数据，重置表单`);
        }
      } else {
        form.resetFields();
        console.log(`[${tabKey}] 缺少参数，重置表单`);
      }
    }
  }, [editingRecord, form, dispatch, tabKey, getTabFormData]);

  const handleFormSubmit = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();

      if (editingRecord) {
        // 更新充值记录
        await dispatch(
          updateRechargeOrder({
            id: editingRecord.id,
            recordData: values,
          })
        ).unwrap();
        message.success("更新成功");
      } else {
        // 添加充值记录
        await dispatch(addRechargeOrder(values)).unwrap();
        message.success("添加成功");
        form.resetFields();
      }

      dispatch(clearSelectedRecord());
      // 清除表单数据（仅在添加模式下）
      if (!editingRecord && tabKey && clearTabFormData) {
        clearTabFormData(tabKey);
      }
      onSave && onSave();
    } catch (error) {
      console.error("操作失败:", error);
      if (error.errorFields) {
        // 表单验证错误
        return;
      }
      message.error(error || "操作失败");
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    dispatch(clearSelectedRecord());
    // 清除表单数据
    if (tabKey && clearTabFormData) {
      clearTabFormData(tabKey);
    }
    onCancel && onCancel();
  };

  return (
    <div>
      <Title level={3}>{editingRecord ? "编辑充值记录" : "添加充值记录"}</Title>
      <Card>
        <Form
          form={form}
          layout="vertical"
          style={{ maxWidth: 800 }}
          onValuesChange={(changedValues, allValues) => {
            // 实时保存表单数据（仅在添加模式下）
            if (!editingRecord && tabKey && saveTabFormData) {
              console.log(`[${tabKey}] 保存表单数据:`, allValues);
              saveTabFormData(tabKey, allValues);
            }
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="用户ID"
                name="userId"
                rules={[{ required: true, message: "请输入用户ID" }]}
              >
                <InputNumber
                  style={{ width: "100%" }}
                  placeholder="请输入用户ID"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="支付方式ID"
                name="paymentId"
                rules={[{ required: true, message: "请输入支付方式ID" }]}
              >
                <InputNumber
                  style={{ width: "100%" }}
                  placeholder="请输入支付方式ID"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="交易ID"
                name="transactionId"
                rules={[{ required: true, message: "请输入交易ID" }]}
              >
                <Input placeholder="请输入交易ID" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="金额"
                name="amount"
                rules={[{ required: true, message: "请输入金额" }]}
              >
                <InputNumber
                  style={{ width: "100%" }}
                  placeholder="请输入金额"
                  min={0}
                  precision={2}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="货币"
                name="currency"
                rules={[{ required: true, message: "请输入货币类型" }]}
              >
                <Input placeholder="请输入货币类型" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="支付时间" name="paymentTime">
                <DatePicker
                  showTime
                  style={{ width: "100%" }}
                  placeholder="请选择支付时间"
                  format="YYYY-MM-DD HH:mm:ss"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="退款金额" name="refundAmount">
                <InputNumber
                  style={{ width: "100%" }}
                  placeholder="请输入退款金额"
                  min={0}
                  precision={2}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="退款时间" name="refundTime">
                <DatePicker
                  showTime
                  style={{ width: "100%" }}
                  placeholder="请选择退款时间"
                  format="YYYY-MM-DD HH:mm:ss"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="支付方式" name="paymentMethod">
                <Input placeholder="请输入支付方式" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="IP地址" name="ip">
                <Input placeholder="请输入IP地址" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item label="备注" name="mark">
            <Input.TextArea placeholder="请输入备注" rows={3} />
          </Form.Item>

          <Form.Item label="扩展数据" name="extraData">
            <Input.TextArea placeholder="请输入扩展数据" rows={3} />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button
                type="primary"
                onClick={handleFormSubmit}
                icon={<SaveOutlined />}
                loading={loading}
              >
                保存
              </Button>
              <Button
                onClick={handleCancel}
                icon={<UndoOutlined />}
                disabled={loading}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
}

export default AddRechargeOrderComponent;
