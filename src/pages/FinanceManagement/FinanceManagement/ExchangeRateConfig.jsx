import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  Card,
  Table,
  Button,
  Space,
  Select,
  DatePicker,
  Typography,
  Row,
  Col,
  message,
  Tag,
  Input,
  InputNumber,
  Popconfirm,
} from "antd";
import PageTabs from "@/components/PageTabs/PageTabs";
import AddExchangeRateComponent from "./AddExchangeRateComponent";
import { usePageTabs } from "@/hooks/usePageTabs";
import { DeleteOutlined } from "@ant-design/icons";
import dayjs from "dayjs";
import {
  fetchExchangeRateList,
  addExchangeRate,
  updateExchangeRate,
  getExchangeRateDetail,
  deleteExchangeRate,
  batchDeleteExchangeRate,
  batchUpdateCurrency,
  updateFilters,
  updatePagination,
  resetState,
  clearError,
} from "@/redux/exchangeRateConfigPage/exchangeRateConfigPageSlice";

const { Title } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

function ExchangeRateConfigPage() {
  const dispatch = useDispatch();
  const {
    list,
    total,
    currentPage,
    pageSize,
    maxPage,
    loading,
    error,
    selectedRateConfig,
    deleteData,
    batchDeleteData,
    filters,
  } = useSelector((state) => state.exchangeRateConfigPage);

  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [dateRange, setDateRange] = useState([null, null]);

  // 计算是否有任何删除操作正在进行，用于覆盖整个表格的加载状态
  const isDeleting = deleteData.loading || batchDeleteData.loading;

  const {
    activeTab,
    editingRecord,
    tabPanes,
    handleAdd,
    handleEdit,
    handleTabChange,
    handleTabEdit,
    handleSaveSuccess,
    handleCancel,
    isListTab,
    createTab,
    saveTabFormData,
    getTabFormData,
    clearTabFormData,
  } = usePageTabs({
    listTabLabel: "汇率配置",
    tabTypes: {
      add: {
        label: "添加汇率配置",
        prefix: "add",
      },
      edit: {
        label: "编辑",
        prefix: "edit",
        getLabelFn: (record) => `编辑汇率配置 - ${record.currency}`,
      },
      batchUpdate: {
        label: "批量修改货币",
        prefix: "batchUpdate",
      },
    },
    dataList: list,
    onSaveSuccess: () => {
      loadData();
    },
  });

  // 符号位置映射
  const symbolFloatMap = {
    1: "量左",
    2: "量右",
  };

  // 加载数据
  const loadData = () => {
    const queryRequest = {
      page: currentPage,
      pageSize,
      currency: filters.currency,
      currencyName: filters.currencyName,
      createTimeStart: filters.createTimeStart,
      createTimeEnd: filters.createTimeEnd,
    };
    dispatch(fetchExchangeRateList(queryRequest));
  };

  useEffect(() => {
    loadData();
    return () => {
      dispatch(resetState());
    };
  }, []);

  // 同步filters中的日期到dateRange状态
  useEffect(() => {
    if (filters.createTimeStart && filters.createTimeEnd) {
      setDateRange([
        dayjs(filters.createTimeStart),
        dayjs(filters.createTimeEnd),
      ]);
    } else if (filters.createTimeStart || filters.createTimeEnd) {
      setDateRange([
        filters.createTimeStart ? dayjs(filters.createTimeStart) : null,
        filters.createTimeEnd ? dayjs(filters.createTimeEnd) : null,
      ]);
    }
  }, [filters.createTimeStart, filters.createTimeEnd]);

  useEffect(() => {
    if (error) {
      message.error(error);
      dispatch(clearError());
    }
  }, [error]);

  // 获取符号位置标签颜色
  const getPositionColor = (position) => {
    switch (position) {
      case "量右":
        return "blue";
      case "量左":
        return "green";
      default:
        return "default";
    }
  };

  // 表格列定义
  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      width: 60,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "国家",
      dataIndex: "country",
      key: "country",
      width: 100,
    },
    {
      title: "货币代码",
      dataIndex: "currency",
      key: "currency",
      width: 120,
    },
    {
      title: "货币名称",
      dataIndex: "currencyName",
      key: "currencyName",
      width: 140,
    },
    {
      title: "符号位置",
      dataIndex: "symbolFloat",
      key: "symbolFloat",
      width: 100,
      render: (symbolFloat) => {
        const position = symbolFloatMap[symbolFloat] || "未知";
        return <Tag color={getPositionColor(position)}>{position}</Tag>;
      },
    },
    {
      title: "货币号码",
      dataIndex: "currencySymbol",
      key: "currencySymbol",
      width: 100,
    },
    {
      title: "汇率",
      dataIndex: "rate",
      key: "rate",
      width: 100,
      sorter: (a, b) => parseFloat(a.rate) - parseFloat(b.rate),
    },
    {
      title: "创建时间",
      dataIndex: "createTime",
      key: "createTime",
      width: 160,
      render: (createTime) =>
        createTime ? dayjs.unix(createTime).format("YYYY-MM-DD HH:mm:ss") : "-",
      sorter: (a, b) => a.createTime - b.createTime,
    },
    {
      title: "更新时间",
      dataIndex: "updateTime",
      key: "updateTime",
      width: 160,
      render: (updateTime) =>
        updateTime ? dayjs.unix(updateTime).format("YYYY-MM-DD HH:mm:ss") : "-",
      sorter: (a, b) => a.updateTime - b.updateTime,
    },
    {
      title: "操作",
      key: "action",
      width: 150,
      fixed: "right",
      render: (_, record) => (
        <Space size="small">
          <Button
            size="small"
            onClick={() => handleEdit(record)}
            style={{
              backgroundColor: "#ff7a00",
              borderColor: "#ff7a00",
              color: "white",
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确认删除"
            description="您确定要删除这个汇率配置吗？"
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button size="small" danger loading={deleteData.loading}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedKeys) => {
      setSelectedRowKeys(selectedKeys);
    },
  };

  // 处理筛选条件变化
  const handleFilterChange = (key, value) => {
    dispatch(updateFilters({ [key]: value }));
  };

  // 处理分页变化
  const handleTableChange = (pagination) => {
    dispatch(
      updatePagination({
        currentPage: pagination.current,
        pageSize: pagination.pageSize,
      })
    );

    const queryRequest = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      currency: filters.currency,
      currencyName: filters.currencyName,
      createTimeStart: filters.createTimeStart,
      createTimeEnd: filters.createTimeEnd,
    };
    dispatch(fetchExchangeRateList(queryRequest));
  };

  // 处理批量修改货币
  const handleBatchModifyRate = () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请选择要修改货币的汇率配置");
      return;
    }
    return createTab("batchUpdate");
  };

  // 处理批量删除
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请选择要删除的汇率配置");
      return;
    }

    try {
      await dispatch(batchDeleteExchangeRate(selectedRowKeys)).unwrap();
      message.success(`已删除 ${selectedRowKeys.length} 个汇率配置`);
      setSelectedRowKeys([]);
      loadData();
    } catch (error) {
      message.error(error || "批量删除失败");
    }
  };

  // 处理删除
  const handleDelete = async (record) => {
    try {
      await dispatch(deleteExchangeRate(record.id)).unwrap();
      message.success(`已删除汇率配置 ID: ${record.id}`);
      loadData();
    } catch (error) {
      message.error(error || "删除失败");
    }
  };

  // 处理查询
  const handleSearch = () => {
    dispatch(updatePagination({ currentPage: 1 }));
    const queryRequest = {
      page: 1,
      pageSize,
      currency: filters.currency,
      currencyName: filters.currencyName,
      createTimeStart: filters.createTimeStart,
      createTimeEnd: filters.createTimeEnd,
    };
    dispatch(fetchExchangeRateList(queryRequest));
  };

  return (
    <Card style={{ backgroundColor: "#fff" }}>
      {/* 页面标签栏 */}
      <div className="page-tabs-wrapper">
        <PageTabs
          activeKey={activeTab}
          onChange={handleTabChange}
          onEdit={handleTabEdit}
          items={tabPanes}
          type="editable-card"
        />
      </div>

      {/* 条件渲染 */}
      {isListTab ? (
        <>
          {/* 筛选条件 */}
          <Card style={{ marginBottom: 16 }}>
            <Row gutter={[16, 16]} align="middle">
              <Col>
                <Input
                  placeholder="货币代码"
                  value={filters.currency}
                  onChange={(e) =>
                    handleFilterChange("currency", e.target.value)
                  }
                  style={{ width: 120 }}
                />
              </Col>
              <Col>
                <Input
                  placeholder="货币名称"
                  value={filters.currencyName}
                  onChange={(e) =>
                    handleFilterChange("currencyName", e.target.value)
                  }
                  style={{ width: 120 }}
                />
              </Col>
              <Col>
                <span>创建时间</span>
              </Col>
              <Col>
                <RangePicker
                  value={dateRange}
                  onChange={(dates) => {
                    setDateRange(dates);
                    handleFilterChange(
                      "createTimeStart",
                      dates && dates[0] ? dates[0].format("YYYY-MM-DD") : null
                    );
                    handleFilterChange(
                      "createTimeEnd",
                      dates && dates[1] ? dates[1].format("YYYY-MM-DD") : null
                    );
                  }}
                  format="YYYY-MM-DD"
                  placeholder={["开始时间", "结束时间"]}
                  style={{ width: 240 }}
                />
              </Col>
              <Col>
                <Button type="primary" onClick={handleSearch} loading={loading}>
                  查询
                </Button>
              </Col>
              <Col>
                <Button
                  onClick={() => {
                    // 清空筛选条件
                    handleFilterChange("currency", "");
                    handleFilterChange("currencyName", "");
                    handleFilterChange("createTimeStart", null);
                    handleFilterChange("createTimeEnd", null);
                    setDateRange([null, null]);
                    dispatch(updatePagination({ currentPage: 1 }));

                    // 使用清空的参数立即触发查询
                    const queryRequest = {
                      page: 1,
                      pageSize,
                      currency: "",
                      currencyName: "",
                      createTimeStart: null,
                      createTimeEnd: null,
                    };
                    dispatch(fetchExchangeRateList(queryRequest));
                    message.success("筛选条件已重置");
                  }}
                >
                  重置
                </Button>
              </Col>
            </Row>
          </Card>

          {/* 批量操作按钮 */}
          <Card style={{ marginBottom: 16 }}>
            <Space wrap>
              <Button
                onClick={handleAdd}
                style={{
                  backgroundColor: "#ff7a00",
                  borderColor: "#ff7a00",
                  color: "white",
                }}
              >
                添加
              </Button>
              <Popconfirm
                title="确认批量删除"
                description={`您确定要删除选中的 ${selectedRowKeys.length} 个汇率配置吗？`}
                onConfirm={handleBatchDelete}
                okText="确定"
                cancelText="取消"
                disabled={selectedRowKeys.length === 0}
              >
                <Button
                  danger
                  icon={<DeleteOutlined />}
                  disabled={selectedRowKeys.length === 0}
                  loading={batchDeleteData.loading}
                >
                  批量删除
                </Button>
              </Popconfirm>
              <Button
                onClick={handleBatchModifyRate}
                disabled={selectedRowKeys.length === 0}
                style={{
                  backgroundColor: "#ff7a00",
                  borderColor: "#ff7a00",
                  color: "white",
                }}
              >
                批量修改货币
              </Button>
            </Space>
          </Card>

          {/* 数据表格 */}
          <Card>
            <Table
              columns={columns}
              dataSource={list.map((item) => ({ ...item, key: item.id }))}
              loading={loading || isDeleting}
              rowSelection={rowSelection}
              pagination={{
                current: currentPage,
                pageSize: pageSize,
                total: total,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `第 ${range[0]}-${range[1]} 条 / 共 ${total} 条`,
                pageSizeOptions: ["10", "20", "50"],
                size: "small",
              }}
              onChange={handleTableChange}
              scroll={{ x: 1400 }}
              size="middle"
              bordered
            />
          </Card>
        </>
      ) : (
        /* 新的添加/编辑组件 */
        <AddExchangeRateComponent
          editingRecord={activeTab.startsWith("add-") ? null : editingRecord}
          selectedRowKeys={
            activeTab.startsWith("batchUpdate-") ? selectedRowKeys : []
          }
          onSave={handleSaveSuccess}
          onCancel={handleCancel}
          tabType={activeTab.split("-")[0]}
          tabKey={activeTab}
          saveTabFormData={saveTabFormData}
          getTabFormData={getTabFormData}
          clearTabFormData={clearTabFormData}
        />
      )}
    </Card>
  );
}

export default ExchangeRateConfigPage;
