import React, { useState, useEffect } from "react";
import {
  Card,
  Table,
  Button,
  Space,
  Select,
  DatePicker,
  Typography,
  Row,
  Col,
  message,
  Tag,
  Form,
  Input,
  InputNumber,
  Popconfirm,
  Modal,
  Alert,
} from "antd";
import PageTabs from "@/components/PageTabs/PageTabs";
import AddPaymentDisputeComponent from "./AddPaymentDisputeComponent";
import { usePageTabs } from "@/hooks/usePageTabs";
import { useGlobalConstants } from "@/hooks/useGlobalConstants";
import { DeleteOutlined } from "@ant-design/icons";
import { useSelector, useDispatch } from "react-redux";
import dayjs from "dayjs";
import {
  loadPaymentDisputeList,
  loadPaymentDisputeDetail,
  addPaymentDispute,
  editPaymentDispute,
  removePaymentDispute,
  batchRemovePaymentDispute,
  batchModifyPaymentDisputeStatus,
  batchModifyPaymentDisputeUser,
  batchModifyPaymentDisputePayment,
  batchModifyPaymentDisputePaymentRecord,
  batchModifyPaymentDisputeManage,
  setFilters,
  setCurrentPage,
  setPageSize,
  clearSelectedPaymentDispute,
  resetState,
} from "@/redux/paymentDisputePage/paymentDisputePageSlice";

const { Title } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

function PaymentDisputePage() {
  const dispatch = useDispatch();
  const {
    list,
    total,
    currentPage,
    pageSize,
    maxPage,
    loading,
    error,
    selectedPaymentDispute,
    deleteData,
    batchDeleteData,
    filters,
  } = useSelector((state) => state.paymentDisputePage);

  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  // 批量操作Modal相关状态
  const [batchStatusModalVisible, setBatchStatusModalVisible] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState("");
  const [batchUserModalVisible, setBatchUserModalVisible] = useState(false);
  const [selectedUserId, setSelectedUserId] = useState("");
  const [batchPaymentRecordModalVisible, setBatchPaymentRecordModalVisible] =
    useState(false);
  const [selectedPaymentRecordId, setSelectedPaymentRecordId] = useState("");
  const [batchPaymentModalVisible, setBatchPaymentModalVisible] =
    useState(false);
  const [selectedPaymentId, setSelectedPaymentId] = useState("");
  const [batchManageModalVisible, setBatchManageModalVisible] = useState(false);
  const [selectedManageId, setSelectedManageId] = useState("");

  // 计算是否有任何删除操作正在进行，用于覆盖整个表格的加载状态
  const isDeleting = deleteData.loading || batchDeleteData.loading;

  // 获取全局枚举常量
  const {
    getSelectOptions,
    getEnumName,
    loading: enumLoading,
  } = useGlobalConstants();

  const {
    activeTab,
    editingRecord,
    tabPanes,
    handleAdd,
    handleEdit,
    handleTabChange,
    handleTabEdit,
    handleSaveSuccess,
    handleCancel,
    isListTab,
    saveTabFormData,
    getTabFormData,
    clearTabFormData,
  } = usePageTabs({
    listTabLabel: "支付争议",
    tabTypes: {
      add: {
        label: "添加支付争议",
        prefix: "add",
      },
      edit: {
        label: "编辑",
        prefix: "edit",
        getLabelFn: (record) => `编辑支付争议 - ${record.id}`,
      },
    },
    dataList: list,
    onSaveSuccess: () => {
      loadData();
    },
  });

  // 争议类型映射 - 使用全局枚举，保留原有硬编码作为后备
  const disputeTypeMap = {
    1: "金额错误",
    2: "重复扣款",
    3: "支付未到账",
    4: "其他",
  };

  // 状态映射 - 使用全局枚举，保留原有硬编码作为后备
  const statusMap = {
    1: "待审核",
    2: "已通过",
    3: "已拒绝",
    4: "已解决",
  };

  // 页面初始化
  useEffect(() => {
    loadData();

    return () => {
      dispatch(resetState());
    };
  }, [dispatch]);

  // 加载数据
  const loadData = async (customParams = {}) => {
    const queryRequest = {
      page: currentPage,
      pageSize: pageSize,
      ...filters,
      ...customParams,
    };

    try {
      await dispatch(loadPaymentDisputeList(queryRequest)).unwrap();
    } catch (error) {
      message.error("加载数据失败: " + error);
    }
  };

  // 获取状态标签颜色 - 根据不同状态显示不同颜色
  const getStatusColor = (status) => {
    const statusStr = String(status);

    // 状态颜色映射表
    const colorMap = {
      // 全局枚举状态 (PaymentDispute.Status)
      0: "processing", // PENDING - 待处理 (蓝色，表示正在处理中)
      1: "success", // RESOLVED - 已解决 (绿色，表示成功完成)
      2: "error", // REJECTED - 已拒绝 (红色，表示被拒绝)

      // 页面硬编码状态（向后兼容）
      3: "error", // 已拒绝 (红色，表示被拒绝)
      4: "success", // 已解决 (绿色，表示成功完成)
    };

    // 特殊处理：检查是否为"待审核"状态
    if (statusStr === "1") {
      const statusName =
        getEnumName("PaymentDispute.Status", status) || statusMap[status];
      if (
        statusName &&
        (statusName.includes("审核") || statusName.includes("待"))
      ) {
        return "warning"; // 橙色，表示等待审核
      }
    }

    // 为"已通过"状态使用特殊颜色
    if (statusStr === "2") {
      const statusName = statusMap[status];
      if (statusName && statusName.includes("通过")) {
        return "success"; // 绿色，表示审核通过
      }
    }

    return colorMap[statusStr] || "default";
  };

  // 表格列定义
  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      width: 60,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "用户",
      dataIndex: ["user", "name"],
      key: "user",
      width: 120,
      render: (text, record) => {
        const user = record.user || {};
        return `${user.name || "未知"} (${user.id || "0"})`;
      },
    },
    {
      title: "支付记录",
      dataIndex: "paymentRecordId",
      key: "paymentRecordId",
      width: 180,
      render: (paymentRecordId) =>
        paymentRecordId ? `支付记录#${paymentRecordId}` : "无",
    },
    {
      title: "支付渠道",
      dataIndex: ["payment", "name"],
      key: "payment",
      width: 140,
      render: (text, record) => {
        const payment = record.payment || {};
        return payment.name ? `${payment.name} (${payment.id || "0"})` : "无";
      },
    },
    {
      title: "争议类型",
      dataIndex: "disputeType",
      key: "disputeType",
      width: 120,
      render: (disputeType) => {
        // 优先使用全局枚举，如果没有找到则使用硬编码映射
        const enumName = getEnumName("PaymentDispute.DisputeType", disputeType);
        return enumName || disputeTypeMap[disputeType] || disputeType;
      },
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      width: 100,
      render: (status) => {
        // 优先使用全局枚举，如果没有找到则使用硬编码映射
        const enumName = getEnumName("PaymentDispute.Status", status);
        const displayName = enumName || statusMap[status] || status;
        return <Tag color={getStatusColor(status)}>{displayName}</Tag>;
      },
    },
    {
      title: "争议时间",
      dataIndex: "disputeTime",
      key: "disputeTime",
      width: 160,
      render: (disputeTime) =>
        disputeTime
          ? dayjs.unix(disputeTime).format("YYYY-MM-DD HH:mm:ss")
          : "-",
      sorter: (a, b) => (a.disputeTime || 0) - (b.disputeTime || 0),
    },
    {
      title: "管理员",
      dataIndex: "manageId",
      key: "manageId",
      width: 120,
      render: (manageId) => (manageId ? `管理员 (${manageId})` : "未分配"),
    },
    {
      title: "审核时间",
      dataIndex: "reviewTime",
      key: "reviewTime",
      width: 160,
      render: (reviewTime) =>
        reviewTime
          ? dayjs.unix(reviewTime).format("YYYY-MM-DD HH:mm:ss")
          : "未审核",
      sorter: (a, b) => {
        if (!a.reviewTime) return 1;
        if (!b.reviewTime) return -1;
        return a.reviewTime - b.reviewTime;
      },
    },
    {
      title: "额外数据",
      dataIndex: "extraData",
      key: "extraData",
      width: 100,
      render: (extraData) => (
        <img
          src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjMyIiBoZWlnaHQ9IjMyIiByeD0iNCIgZmlsbD0iI2Y0ZjRmNCIvPgo8cGF0aCBkPSJNOCA4SDI0VjI0SDhaIiBmaWxsPSIjZDlkOWQ5Ii8+Cjx0ZXh0IHg9IjE2IiB5PSIxOCIgZm9udC1zaXplPSI4IiBmaWxsPSIjNjY2IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7lh63orIE8L3RleHQ+Cjwvc3ZnPgo="
          alt="凭证"
          style={{ width: 24, height: 24, cursor: "pointer" }}
          title={extraData || "凭证"}
        />
      ),
    },
    {
      title: "操作",
      key: "action",
      width: 200,
      fixed: "right",
      render: (_, record) => (
        <Space size="small">
          {record.status === "1" ? (
            <>
              <Button
                size="small"
                onClick={() => handleAudit(record)}
                style={{
                  backgroundColor: "#52c41a",
                  borderColor: "#52c41a",
                  color: "white",
                }}
              >
                审核
              </Button>
              <Button
                size="small"
                onClick={() => handleEdit(record)}
                style={{
                  backgroundColor: "#ff7a00",
                  borderColor: "#ff7a00",
                  color: "white",
                }}
              >
                编辑
              </Button>
            </>
          ) : (
            <Button
              size="small"
              onClick={() => handleEdit(record)}
              style={{
                backgroundColor: "#ff7a00",
                borderColor: "#ff7a00",
                color: "white",
              }}
            >
              编辑
            </Button>
          )}
          <Popconfirm
            title="确定要删除此争议记录吗？"
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button size="small" danger loading={deleteData.loading}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedKeys) => {
      setSelectedRowKeys(selectedKeys);
    },
  };

  // 处理批量删除
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请选择要删除的争议记录");
      return;
    }

    try {
      await dispatch(batchRemovePaymentDispute(selectedRowKeys)).unwrap();
      message.success(`已删除 ${selectedRowKeys.length} 个争议记录`);
      setSelectedRowKeys([]);
      loadData();
    } catch (error) {
      message.error("批量删除失败: " + error);
    }
  };

  // 处理批量修改状态
  const handleBatchModifyStatus = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请选择要修改状态的争议记录");
      return;
    }
    setBatchStatusModalVisible(true);
    setSelectedStatus("");
  };

  const handleBatchStatusConfirm = async () => {
    if (!selectedStatus) {
      message.warning("请选择状态");
      return;
    }

    try {
      await dispatch(
        batchModifyPaymentDisputeStatus({
          ids: selectedRowKeys,
          status: selectedStatus,
        })
      ).unwrap();
      message.success(`已修改 ${selectedRowKeys.length} 个争议记录的状态`);
      setSelectedRowKeys([]);
      loadData();
      setBatchStatusModalVisible(false);
    } catch (error) {
      message.error("批量修改状态失败: " + error);
    }
  };

  const handleBatchStatusCancel = () => {
    setBatchStatusModalVisible(false);
    setSelectedStatus("");
  };

  // 处理批量修改用户
  const handleBatchModifyUser = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请选择要修改的争议记录");
      return;
    }
    setBatchUserModalVisible(true);
    setSelectedUserId("");
  };

  const handleBatchUserConfirm = async () => {
    if (!selectedUserId) {
      message.warning("请输入用户ID");
      return;
    }

    try {
      await dispatch(
        batchModifyPaymentDisputeUser({
          ids: selectedRowKeys,
          userId: parseInt(selectedUserId),
        })
      ).unwrap();
      message.success(`已修改 ${selectedRowKeys.length} 个争议记录的用户信息`);
      setSelectedRowKeys([]);
      loadData();
      setBatchUserModalVisible(false);
    } catch (error) {
      message.error("批量修改用户失败: " + error);
    }
  };

  const handleBatchUserCancel = () => {
    setBatchUserModalVisible(false);
    setSelectedUserId("");
  };

  // 处理批量修改支付记录
  const handleBatchModifyPayment = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请选择要修改的争议记录");
      return;
    }
    setBatchPaymentRecordModalVisible(true);
    setSelectedPaymentRecordId("");
  };

  const handleBatchPaymentRecordConfirm = async () => {
    if (!selectedPaymentRecordId) {
      message.warning("请输入支付记录ID");
      return;
    }

    try {
      await dispatch(
        batchModifyPaymentDisputePaymentRecord({
          ids: selectedRowKeys,
          paymentRecordId: parseInt(selectedPaymentRecordId),
        })
      ).unwrap();
      message.success(`已修改 ${selectedRowKeys.length} 个争议记录的支付记录`);
      setSelectedRowKeys([]);
      loadData();
      setBatchPaymentRecordModalVisible(false);
    } catch (error) {
      message.error("批量修改支付记录失败: " + error);
    }
  };

  const handleBatchPaymentRecordCancel = () => {
    setBatchPaymentRecordModalVisible(false);
    setSelectedPaymentRecordId("");
  };

  // 处理批量修改支付渠道
  const handleBatchModifyChannel = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请选择要修改的争议记录");
      return;
    }
    setBatchPaymentModalVisible(true);
    setSelectedPaymentId("");
  };

  const handleBatchPaymentConfirm = async () => {
    if (!selectedPaymentId) {
      message.warning("请输入支付渠道ID");
      return;
    }

    try {
      await dispatch(
        batchModifyPaymentDisputePayment({
          ids: selectedRowKeys,
          paymentId: parseInt(selectedPaymentId),
        })
      ).unwrap();
      message.success(`已修改 ${selectedRowKeys.length} 个争议记录的支付渠道`);
      setSelectedRowKeys([]);
      loadData();
      setBatchPaymentModalVisible(false);
    } catch (error) {
      message.error("批量修改支付渠道失败: " + error);
    }
  };

  const handleBatchPaymentCancel = () => {
    setBatchPaymentModalVisible(false);
    setSelectedPaymentId("");
  };

  // 处理批量修改管理员
  const handleBatchModifyAdmin = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请选择要修改的争议记录");
      return;
    }
    setBatchManageModalVisible(true);
    setSelectedManageId("");
  };

  const handleBatchManageConfirm = async () => {
    if (!selectedManageId) {
      message.warning("请输入管理员ID");
      return;
    }

    try {
      await dispatch(
        batchModifyPaymentDisputeManage({
          ids: selectedRowKeys,
          manageId: parseInt(selectedManageId),
        })
      ).unwrap();
      message.success(`已修改 ${selectedRowKeys.length} 个争议记录的管理员`);
      setSelectedRowKeys([]);
      loadData();
      setBatchManageModalVisible(false);
    } catch (error) {
      message.error("批量修改管理员失败: " + error);
    }
  };

  const handleBatchManageCancel = () => {
    setBatchManageModalVisible(false);
    setSelectedManageId("");
  };

  // 处理审核
  const handleAudit = (record) => {
    Modal.confirm({
      title: "审核争议记录",
      content: `确定要审核争议记录 ID: ${record.id} 吗？`,
      onOk: async () => {
        try {
          // 将状态改为已通过
          await dispatch(
            editPaymentDispute({
              id: record.id,
              paymentDisputeData: { ...record, status: "2" },
            })
          ).unwrap();
          message.success("审核通过");
          loadData();
        } catch (error) {
          message.error("审核失败: " + error);
        }
      },
    });
  };

  // 处理删除
  const handleDelete = async (record) => {
    try {
      await dispatch(removePaymentDispute(record.id)).unwrap();
      message.success(`已删除争议记录 ID: ${record.id}`);
      loadData();
    } catch (error) {
      message.error("删除失败: " + error);
    }
  };

  // 处理查询
  const handleSearch = async () => {
    dispatch(setCurrentPage(1));
    await loadData({ page: 1 });
  };

  // 处理筛选条件变化
  const handleFilterChange = (key, value) => {
    dispatch(setFilters({ [key]: value }));
  };

  // 处理时间范围变化
  const handleDateRangeChange = (dates) => {
    if (dates && dates.length === 2) {
      dispatch(
        setFilters({
          createTimeStart: dates[0].toISOString(),
          createTimeEnd: dates[1].toISOString(),
        })
      );
    } else {
      dispatch(
        setFilters({
          createTimeStart: null,
          createTimeEnd: null,
        })
      );
    }
  };

  // 处理分页变化
  const handleTableChange = async (pagination) => {
    dispatch(setCurrentPage(pagination.current));
    dispatch(setPageSize(pagination.pageSize));

    // 直接加载数据而不依赖useEffect
    await loadData({
      page: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  // 处理重置
  const handleReset = () => {
    dispatch(
      setFilters({
        userId: "",
        paymentRecordId: "",
        paymentId: "",
        disputeType: "",
        status: "",
        manageId: "",
        createTimeStart: null,
        createTimeEnd: null,
      })
    );
    dispatch(setCurrentPage(1));
    loadData();
    message.success("筛选条件已重置");
  };

  // 批量修改支付记录
  const handleBatchModifyPaymentRecord = () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请选择要修改的争议记录");
      return;
    }
    message.info("批量修改支付记录功能待实现");
  };

  // 批量修改管理员
  const handleBatchModifyManage = () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请选择要修改的争议记录");
      return;
    }
    message.info("批量修改管理员功能待实现");
  };

  return (
    <Card style={{ backgroundColor: "#fff" }}>
      {/* 页面标签栏 */}
      <div className="page-tabs-wrapper">
        <PageTabs
          activeKey={activeTab}
          onChange={handleTabChange}
          onEdit={handleTabEdit}
          items={tabPanes}
          type="editable-card"
        />
      </div>

      {/* 条件渲染 */}
      {isListTab ? (
        <>
          {/* 筛选条件 */}
          <Card style={{ marginBottom: 16 }}>
            <Row gutter={[16, 16]} align="middle">
              <Col>
                <InputNumber
                  placeholder="用户ID"
                  value={filters.userId}
                  onChange={(value) => handleFilterChange("userId", value)}
                  style={{ width: 120 }}
                />
              </Col>
              <Col>
                <InputNumber
                  placeholder="支付记录ID"
                  value={filters.paymentRecordId}
                  onChange={(value) =>
                    handleFilterChange("paymentRecordId", value)
                  }
                  style={{ width: 150 }}
                />
              </Col>
              <Col>
                <InputNumber
                  placeholder="支付渠道ID"
                  value={filters.paymentId}
                  onChange={(value) => handleFilterChange("paymentId", value)}
                  style={{ width: 120 }}
                />
              </Col>
              <Col>
                <Select
                  value={filters.status}
                  onChange={(value) => handleFilterChange("status", value)}
                  style={{ width: 120 }}
                  placeholder="状态"
                >
                  <Option value="">全部状态</Option>
                  <Option value="1">待审核</Option>
                  <Option value="2">已通过</Option>
                  <Option value="3">已拒绝</Option>
                  <Option value="4">已解决</Option>
                </Select>
              </Col>
              <Col>
                <InputNumber
                  placeholder="管理员ID"
                  value={filters.manageId}
                  onChange={(value) => handleFilterChange("manageId", value)}
                  style={{ width: 120 }}
                />
              </Col>
              <Col>
                <span>创建时间</span>
              </Col>
              <Col>
                <RangePicker
                  onChange={handleDateRangeChange}
                  style={{ width: 240 }}
                  value={
                    filters.createTimeStart && filters.createTimeEnd
                      ? [
                          dayjs(filters.createTimeStart),
                          dayjs(filters.createTimeEnd),
                        ]
                      : null
                  }
                />
              </Col>
              <Col>
                <Button type="primary" onClick={handleSearch}>
                  查询
                </Button>
              </Col>
              <Col>
                <Button onClick={handleReset}>重置</Button>
              </Col>
            </Row>
          </Card>

          {/* 批量操作按钮 */}
          <Card style={{ marginBottom: 16 }}>
            <Space wrap>
              <Button
                onClick={handleAdd}
                style={{
                  backgroundColor: "#ff7a00",
                  borderColor: "#ff7a00",
                  color: "white",
                }}
              >
                添加
              </Button>
              <Popconfirm
                title={`确定要删除选中的 ${selectedRowKeys.length} 个争议记录吗？`}
                onConfirm={handleBatchDelete}
                okText="确定"
                cancelText="取消"
              >
                <Button
                  danger
                  icon={<DeleteOutlined />}
                  disabled={selectedRowKeys.length === 0}
                  loading={batchDeleteData.loading}
                >
                  批量删除
                </Button>
              </Popconfirm>
              <Button
                onClick={handleBatchModifyStatus}
                disabled={selectedRowKeys.length === 0}
                style={{
                  backgroundColor: "#ff7a00",
                  borderColor: "#ff7a00",
                  color: "white",
                }}
              >
                批量修改状态
              </Button>
              <Button
                onClick={handleBatchModifyUser}
                disabled={selectedRowKeys.length === 0}
                style={{
                  backgroundColor: "#ff7a00",
                  borderColor: "#ff7a00",
                  color: "white",
                }}
              >
                批量修改用户
              </Button>
              <Button
                onClick={handleBatchModifyPayment}
                disabled={selectedRowKeys.length === 0}
                style={{
                  backgroundColor: "#ff7a00",
                  borderColor: "#ff7a00",
                  color: "white",
                }}
              >
                批量修改支付渠道
              </Button>
              <Button
                onClick={handleBatchModifyPaymentRecord}
                disabled={selectedRowKeys.length === 0}
                style={{
                  backgroundColor: "#ff7a00",
                  borderColor: "#ff7a00",
                  color: "white",
                }}
              >
                批量修改支付记录
              </Button>
              <Button
                onClick={handleBatchModifyManage}
                disabled={selectedRowKeys.length === 0}
                style={{
                  backgroundColor: "#ff7a00",
                  borderColor: "#ff7a00",
                  color: "white",
                }}
              >
                批量修改管理员
              </Button>
            </Space>
          </Card>

          {/* 数据表格 */}
          <Card>
            <Table
              columns={columns}
              dataSource={Array.isArray(list) ? list : []}
              loading={loading || isDeleting}
              rowSelection={rowSelection}
              rowKey="id"
              pagination={{
                current: currentPage,
                pageSize: pageSize,
                total: total,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `第 ${range[0]}-${range[1]} 条 / 共 ${total} 条`,
                pageSizeOptions: ["10", "20", "50"],
                size: "small",
                onChange: (page, size) =>
                  handleTableChange({ current: page, pageSize: size }),
              }}
              scroll={{ x: 1800 }}
              size="middle"
              bordered
            />
          </Card>
        </>
      ) : (
        /* 新的添加/编辑组件 */
        <AddPaymentDisputeComponent
          editingRecord={activeTab.startsWith("add-") ? null : editingRecord}
          onSave={handleSaveSuccess}
          onCancel={handleCancel}
          tabKey={activeTab}
          saveTabFormData={saveTabFormData}
          getTabFormData={getTabFormData}
          clearTabFormData={clearTabFormData}
        />
      )}

      {/* 批量修改状态Modal */}
      <Modal
        title="批量修改状态"
        open={batchStatusModalVisible}
        onOk={handleBatchStatusConfirm}
        onCancel={handleBatchStatusCancel}
        confirmLoading={loading}
      >
        <Alert
          message={`将为选中的 ${selectedRowKeys.length} 条记录修改状态`}
          type="info"
          style={{ marginBottom: 16 }}
        />
        <div>
          <label>选择新状态：</label>
          <Select
            style={{ width: "100%", marginTop: 8 }}
            placeholder="请选择状态"
            value={selectedStatus}
            onChange={setSelectedStatus}
            options={[
              { label: "待审核", value: "1" },
              { label: "已通过", value: "2" },
              { label: "已拒绝", value: "3" },
              { label: "已解决", value: "4" },
            ]}
          />
        </div>
      </Modal>

      {/* 批量修改用户Modal */}
      <Modal
        title="批量修改用户"
        open={batchUserModalVisible}
        onOk={handleBatchUserConfirm}
        onCancel={handleBatchUserCancel}
        confirmLoading={loading}
      >
        <Alert
          message={`将为选中的 ${selectedRowKeys.length} 条记录修改用户信息`}
          type="info"
          style={{ marginBottom: 16 }}
        />
        <div>
          <label>输入新的用户ID：</label>
          <InputNumber
            style={{ width: "100%", marginTop: 8 }}
            placeholder="请输入用户ID"
            value={selectedUserId}
            onChange={setSelectedUserId}
          />
        </div>
      </Modal>

      {/* 批量修改支付记录Modal */}
      <Modal
        title="批量修改支付记录"
        open={batchPaymentRecordModalVisible}
        onOk={handleBatchPaymentRecordConfirm}
        onCancel={handleBatchPaymentRecordCancel}
        confirmLoading={loading}
      >
        <Alert
          message={`将为选中的 ${selectedRowKeys.length} 条记录修改支付记录`}
          type="info"
          style={{ marginBottom: 16 }}
        />
        <div>
          <label>输入新的支付记录ID：</label>
          <InputNumber
            style={{ width: "100%", marginTop: 8 }}
            placeholder="请输入支付记录ID"
            value={selectedPaymentRecordId}
            onChange={setSelectedPaymentRecordId}
          />
        </div>
      </Modal>

      {/* 批量修改支付渠道Modal */}
      <Modal
        title="批量修改支付渠道"
        open={batchPaymentModalVisible}
        onOk={handleBatchPaymentConfirm}
        onCancel={handleBatchPaymentCancel}
        confirmLoading={loading}
      >
        <Alert
          message={`将为选中的 ${selectedRowKeys.length} 条记录修改支付渠道`}
          type="info"
          style={{ marginBottom: 16 }}
        />
        <div>
          <label>输入新的支付渠道ID：</label>
          <InputNumber
            style={{ width: "100%", marginTop: 8 }}
            placeholder="请输入支付渠道ID"
            value={selectedPaymentId}
            onChange={setSelectedPaymentId}
          />
        </div>
      </Modal>

      {/* 批量修改管理员Modal */}
      <Modal
        title="批量修改管理员"
        open={batchManageModalVisible}
        onOk={handleBatchManageConfirm}
        onCancel={handleBatchManageCancel}
        confirmLoading={loading}
      >
        <Alert
          message={`将为选中的 ${selectedRowKeys.length} 条记录修改管理员`}
          type="info"
          style={{ marginBottom: 16 }}
        />
        <div>
          <label>输入新的管理员ID：</label>
          <InputNumber
            style={{ width: "100%", marginTop: 8 }}
            placeholder="请输入管理员ID"
            value={selectedManageId}
            onChange={setSelectedManageId}
          />
        </div>
      </Modal>
    </Card>
  );
}

export default PaymentDisputePage;
