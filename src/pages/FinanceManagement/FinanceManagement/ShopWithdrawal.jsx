import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  Card,
  Table,
  Button,
  Space,
  Select,
  DatePicker,
  Row,
  Col,
  message,
  Tag,
  Input,
  Popconfirm,
  Modal,
  Alert,
} from "antd";
import PageTabs from "@/components/PageTabs/PageTabs";
import AddShopWithdrawalComponent from "./AddShopWithdrawalComponent";
import { usePageTabs } from "@/hooks/usePageTabs";
import { useGlobalConstants } from "@/hooks/useGlobalConstants";
import { DeleteOutlined } from "@ant-design/icons";
import dayjs from "dayjs";
import {
  fetchWithdrawalList,
  deleteWithdrawal,
  batchDeleteWithdrawals,
  batchUpdateStatus,
  batchUpdateStore,
  batchUpdateWallet,
  updateFilters,
  updatePagination,
  resetState,
} from "@/redux/shopWithdrawalPage/shopWithdrawalPageSlice";

const { RangePicker } = DatePicker;

function ShopWithdrawalPage() {
  const dispatch = useDispatch();
  const { getEnumName, getSelectOptions } = useGlobalConstants();
  const {
    list,
    total,
    currentPage,
    pageSize,
    maxPage,
    loading,
    error,
    selectedWithdrawal,
    deleteData,
    batchDeleteData,
    filters,
  } = useSelector((state) => state.shopWithdrawalPage);

  // 状态颜色映射 - 基于枚举 ID
  const getStatusColor = (statusId) => {
    switch (statusId) {
      case 0: // PENDING - 待审核
        return "orange";
      case 1: // COMPLETED - 已完成
        return "green";
      case 2: // REJECTED - 已拒绝
        return "red";
      default:
        return "default";
    }
  };

  // 计算是否有任何删除操作正在进行，用于覆盖整个表格的加载状态
  const isDeleting = deleteData.loading || batchDeleteData.loading;

  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  // 批量操作Modal相关状态
  const [batchStatusModalVisible, setBatchStatusModalVisible] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState("");
  const [batchStoreModalVisible, setBatchStoreModalVisible] = useState(false);
  const [selectedStoreId, setSelectedStoreId] = useState("");
  const [batchWalletModalVisible, setBatchWalletModalVisible] = useState(false);
  const [selectedWalletId, setSelectedWalletId] = useState("");

  // 筛选条件状态
  const [localFilters, setLocalFilters] = useState({
    storeId: "",
    userWalletId: "",
    userId: "",
    status: "",
    dateRange: [null, null],
  });

  const {
    activeTab,
    editingRecord,
    tabPanes,
    handleAdd,
    handleEdit,
    handleTabChange,
    handleTabEdit,
    handleSaveSuccess,
    handleCancel,
    isListTab,
    saveTabFormData,
    getTabFormData,
    clearTabFormData,
  } = usePageTabs({
    listTabLabel: "店铺提现记录",
    tabTypes: {
      add: {
        label: "添加提现记录",
        prefix: "add",
      },
      edit: {
        label: "编辑",
        prefix: "edit",
        getLabelFn: (record) => `编辑提现记录 - ${record.id}`,
      },
    },
    dataList: list,
    onSaveSuccess: () => {
      loadData();
    },
  });

  // 组件加载时获取数据
  useEffect(() => {
    loadData();
    return () => {
      dispatch(resetState());
    };
  }, []);

  // 加载数据
  const loadData = (customPage = currentPage, customPageSize = pageSize) => {
    const queryRequest = {
      page: customPage,
      pageSize: customPageSize,
      ...filters,
    };
    dispatch(fetchWithdrawalList(queryRequest));
  };

  // 表格列定义
  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      width: 60,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "用户",
      dataIndex: "user",
      key: "user",
      width: 120,
      render: (user) => (user ? `${user.name} (${user.id})` : "-"),
    },
    {
      title: "店铺",
      dataIndex: "store",
      key: "store",
      width: 150,
      render: (store) => (store ? `${store.name} (${store.id})` : "-"),
    },
    {
      title: "钱包",
      dataIndex: "userWallet",
      key: "userWallet",
      width: 150,
      render: (userWallet) =>
        userWallet ? `${userWallet.name} (${userWallet.id})` : "-",
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      width: 100,
      render: (status) => {
        const statusName = getEnumName("StoreWithdraw.Status", status);
        return <Tag color={getStatusColor(status)}>{statusName || status}</Tag>;
      },
    },
    {
      title: "提现金额",
      dataIndex: "withdrawAmount",
      key: "withdrawAmount",
      width: 100,
      sorter: (a, b) => a.withdrawAmount - b.withdrawAmount,
    },
    {
      title: "转账金额",
      dataIndex: "transferAmount",
      key: "transferAmount",
      width: 100,
      sorter: (a, b) => a.transferAmount - b.transferAmount,
    },
    {
      title: "固定费用",
      dataIndex: "feeFixed",
      key: "feeFixed",
      width: 100,
      sorter: (a, b) => a.feeFixed - b.feeFixed,
    },
    {
      title: "百分比费用",
      dataIndex: "feePercent",
      key: "feePercent",
      width: 110,
      sorter: (a, b) => a.feePercent - b.feePercent,
    },
    {
      title: "创建时间",
      dataIndex: "createTime",
      key: "createTime",
      width: 160,
      render: (timestamp) =>
        timestamp ? dayjs.unix(timestamp).format("YYYY-MM-DD HH:mm:ss") : "-",
      sorter: (a, b) => a.createTime - b.createTime,
    },
    {
      title: "支付凭证",
      dataIndex: "proofDocument",
      key: "proofDocument",
      width: 100,
      render: (proofDocument) =>
        proofDocument ? (
          <img
            src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjMyIiBoZWlnaHQ9IjMyIiByeD0iNCIgZmlsbD0iI2Y0ZjRmNCIvPgo8cGF0aCBkPSJNOCA4SDI0VjI0SDhaIiBmaWxsPSIjZDlkOWQ5Ii8+Cjx0ZXh0IHg9IjE2IiB5PSIxOCIgZm9udC1zaXplPSI4IiBmaWxsPSIjNjY2IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7lh63orIE8L3RleHQ+Cjwvc3ZnPgo="
            alt="凭证"
            style={{ width: 24, height: 24, cursor: "pointer" }}
          />
        ) : (
          "-"
        ),
    },
    {
      title: "操作",
      key: "action",
      width: 200,
      fixed: "right",
      render: (_, record) => (
        <Space size="small">
          {record.status === 0 ? ( // PENDING - 待审核
            <>
              <Button
                size="small"
                onClick={() => handleAudit(record)}
                style={{
                  backgroundColor: "#52c41a",
                  borderColor: "#52c41a",
                  color: "white",
                }}
              >
                审核
              </Button>
              <Button
                size="small"
                onClick={() => handleRetry(record)}
                style={{
                  backgroundColor: "#ff7a00",
                  borderColor: "#ff7a00",
                  color: "white",
                }}
              >
                重试
              </Button>
            </>
          ) : (
            <Button
              size="small"
              onClick={() => handleEdit(record)}
              style={{
                backgroundColor: "#ff7a00",
                borderColor: "#ff7a00",
                color: "white",
              }}
            >
              编辑
            </Button>
          )}
          <Popconfirm
            title="确定要删除这条提现记录吗？"
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button size="small" danger loading={deleteData.loading}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedKeys) => {
      setSelectedRowKeys(selectedKeys);
    },
  };

  // 处理批量删除
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请选择要删除的提现记录");
      return;
    }

    try {
      await dispatch(batchDeleteWithdrawals(selectedRowKeys)).unwrap();
      message.success(`已删除 ${selectedRowKeys.length} 个提现记录`);
      setSelectedRowKeys([]);
      loadData();
    } catch (error) {
      message.error("批量删除失败: " + error);
    }
  };

  // 处理批量修改状态
  const handleBatchModifyStatus = () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请选择要修改状态的提现记录");
      return;
    }
    setBatchStatusModalVisible(true);
    setSelectedStatus("");
  };

  const handleBatchStatusConfirm = async () => {
    if (!selectedStatus) {
      message.warning("请选择状态");
      return;
    }

    try {
      await dispatch(
        batchUpdateStatus({ ids: selectedRowKeys, status: selectedStatus })
      ).unwrap();
      message.success(`已修改 ${selectedRowKeys.length} 个提现记录的状态`);
      setSelectedRowKeys([]);
      loadData();
      setBatchStatusModalVisible(false);
    } catch (error) {
      message.error("批量修改状态失败: " + error);
    }
  };

  const handleBatchStatusCancel = () => {
    setBatchStatusModalVisible(false);
    setSelectedStatus("");
  };

  // 处理批量修改店铺
  const handleBatchModifyStore = () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请选择要修改的提现记录");
      return;
    }
    setBatchStoreModalVisible(true);
    setSelectedStoreId("");
  };

  const handleBatchStoreConfirm = async () => {
    if (!selectedStoreId) {
      message.warning("请输入店铺ID");
      return;
    }

    try {
      await dispatch(
        batchUpdateStore({
          ids: selectedRowKeys,
          storeId: parseInt(selectedStoreId),
        })
      ).unwrap();
      message.success(`已修改 ${selectedRowKeys.length} 个提现记录的店铺信息`);
      setSelectedRowKeys([]);
      loadData();
      setBatchStoreModalVisible(false);
    } catch (error) {
      message.error("批量修改店铺失败: " + error);
    }
  };

  const handleBatchStoreCancel = () => {
    setBatchStoreModalVisible(false);
    setSelectedStoreId("");
  };

  // 处理批量修改钱包
  const handleBatchModifyWallet = () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请选择要修改的提现记录");
      return;
    }
    setBatchWalletModalVisible(true);
    setSelectedWalletId("");
  };

  const handleBatchWalletConfirm = async () => {
    if (!selectedWalletId) {
      message.warning("请输入钱包ID");
      return;
    }

    try {
      await dispatch(
        batchUpdateWallet({
          ids: selectedRowKeys,
          walletId: parseInt(selectedWalletId),
        })
      ).unwrap();
      message.success(`已修改 ${selectedRowKeys.length} 个提现记录的钱包信息`);
      setSelectedRowKeys([]);
      loadData();
      setBatchWalletModalVisible(false);
    } catch (error) {
      message.error("批量修改钱包失败: " + error);
    }
  };

  const handleBatchWalletCancel = () => {
    setBatchWalletModalVisible(false);
    setSelectedWalletId("");
  };

  // 处理审核
  const handleAudit = (record) => {
    message.info(`审核提现记录 ID: ${record.id}`);
  };

  // 处理重试
  const handleRetry = (record) => {
    message.info(`重试提现 ID: ${record.id}`);
  };

  // 处理删除
  const handleDelete = async (record) => {
    try {
      await dispatch(deleteWithdrawal(record.id)).unwrap();
      message.success(`已删除提现记录 ID: ${record.id}`);
      loadData();
    } catch (error) {
      message.error("删除失败: " + error);
    }
  };

  // 处理查询
  const handleSearch = () => {
    const queryFilters = {
      storeId: localFilters.storeId || null,
      userWalletId: localFilters.userWalletId || null,
      userId: localFilters.userId || null,
      status: localFilters.status || null,
      createTimeStart: localFilters.dateRange?.[0]
        ? localFilters.dateRange[0].startOf("day")
        : null,
      createTimeEnd: localFilters.dateRange?.[1]
        ? localFilters.dateRange[1].endOf("day")
        : null,
    };

    dispatch(updateFilters(queryFilters));
    dispatch(updatePagination({ currentPage: 1 }));

    // 直接使用新的查询条件来加载数据
    const queryRequest = {
      page: 1,
      pageSize: pageSize,
      ...queryFilters,
    };
    dispatch(fetchWithdrawalList(queryRequest));
  };

  // 处理分页变化
  const handleTableChange = (pagination) => {
    dispatch(
      updatePagination({
        currentPage: pagination.current,
        pageSize: pagination.pageSize,
      })
    );

    // 直接使用新的分页参数来加载数据
    loadData(pagination.current, pagination.pageSize);
  };

  // 处理重置
  const handleReset = () => {
    setLocalFilters({
      storeId: "",
      userWalletId: "",
      userId: "",
      status: "",
      dateRange: [null, null],
    });
    dispatch(
      updateFilters({
        storeId: null,
        userWalletId: null,
        userId: null,
        status: null,
        createTimeStart: null,
        createTimeEnd: null,
      })
    );
    dispatch(updatePagination({ currentPage: 1 }));
    loadData(1, pageSize);
    message.success("筛选条件已重置");
  };

  return (
    <Card style={{ backgroundColor: "#fff" }}>
      {/* 页面标签栏 */}
      <div className="page-tabs-wrapper">
        <PageTabs
          activeKey={activeTab}
          onChange={handleTabChange}
          onEdit={handleTabEdit}
          items={tabPanes}
          type="editable-card"
        />
      </div>

      {/* 条件渲染 */}
      {isListTab ? (
        <>
          {/* 筛选条件 */}
          <Card style={{ marginBottom: 16 }}>
            <Row gutter={[16, 16]} align="middle">
              <Col>
                <Input
                  placeholder="店铺ID"
                  value={localFilters.storeId}
                  onChange={(e) =>
                    setLocalFilters({
                      ...localFilters,
                      storeId: e.target.value,
                    })
                  }
                  style={{ width: 120 }}
                />
              </Col>
              <Col>
                <Input
                  placeholder="钱包ID"
                  value={localFilters.userWalletId}
                  onChange={(e) =>
                    setLocalFilters({
                      ...localFilters,
                      userWalletId: e.target.value,
                    })
                  }
                  style={{ width: 120 }}
                />
              </Col>
              <Col>
                <Input
                  placeholder="用户ID"
                  value={localFilters.userId}
                  onChange={(e) =>
                    setLocalFilters({ ...localFilters, userId: e.target.value })
                  }
                  style={{ width: 120 }}
                />
              </Col>
              <Col>
                <Select
                  placeholder="选择状态"
                  value={localFilters.status}
                  onChange={(value) =>
                    setLocalFilters({ ...localFilters, status: value })
                  }
                  style={{ width: 120 }}
                  allowClear
                  options={getSelectOptions("StoreWithdraw.Status")}
                />
              </Col>
              <Col>
                <RangePicker
                  value={localFilters.dateRange}
                  onChange={(dates) =>
                    setLocalFilters({
                      ...localFilters,
                      dateRange: dates || [null, null],
                    })
                  }
                  style={{ width: 240 }}
                />
              </Col>
              <Col>
                <Button type="primary" onClick={handleSearch} loading={loading}>
                  查询
                </Button>
              </Col>
              <Col>
                <Button onClick={handleReset}>重置</Button>
              </Col>
            </Row>
          </Card>

          {/* 批量操作按钮 */}
          <Card style={{ marginBottom: 16 }}>
            <Space wrap>
              <Button
                onClick={handleAdd}
                style={{
                  backgroundColor: "#ff7a00",
                  borderColor: "#ff7a00",
                  color: "white",
                }}
              >
                添加
              </Button>
              <Popconfirm
                title={`确定要删除选中的 ${selectedRowKeys.length} 条提现记录吗？`}
                onConfirm={handleBatchDelete}
                disabled={selectedRowKeys.length === 0}
                okText="确定"
                cancelText="取消"
              >
                <Button
                  danger
                  icon={<DeleteOutlined />}
                  disabled={selectedRowKeys.length === 0}
                  loading={batchDeleteData.loading}
                >
                  批量删除
                </Button>
              </Popconfirm>
              <Button
                onClick={handleBatchModifyStatus}
                disabled={selectedRowKeys.length === 0}
                style={{
                  backgroundColor: "#ff7a00",
                  borderColor: "#ff7a00",
                  color: "white",
                }}
              >
                批量修改状态
              </Button>
              <Button
                onClick={handleBatchModifyStore}
                disabled={selectedRowKeys.length === 0}
                style={{
                  backgroundColor: "#ff7a00",
                  borderColor: "#ff7a00",
                  color: "white",
                }}
              >
                批量修改店铺
              </Button>
              <Button
                onClick={handleBatchModifyWallet}
                disabled={selectedRowKeys.length === 0}
                style={{
                  backgroundColor: "#ff7a00",
                  borderColor: "#ff7a00",
                  color: "white",
                }}
              >
                批量修改钱包
              </Button>
            </Space>
          </Card>

          {/* 数据表格 */}
          <Card>
            <Table
              columns={columns}
              dataSource={list}
              loading={loading || isDeleting}
              rowSelection={rowSelection}
              rowKey="id"
              pagination={{
                current: currentPage,
                pageSize: pageSize,
                total: total,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `第 ${range[0]}-${range[1]} 条 / 共 ${total} 条`,
                pageSizeOptions: ["10", "20", "50"],
                size: "small",
              }}
              onChange={handleTableChange}
              scroll={{ x: 1600 }}
              size="middle"
              bordered
            />
          </Card>
        </>
      ) : (
        /* 新的添加/编辑组件 */
        <AddShopWithdrawalComponent
          editingRecord={activeTab.startsWith("add-") ? null : editingRecord}
          onSave={handleSaveSuccess}
          onCancel={handleCancel}
          tabKey={activeTab}
          saveTabFormData={saveTabFormData}
          getTabFormData={getTabFormData}
          clearTabFormData={clearTabFormData}
        />
      )}

      {/* 批量修改状态Modal */}
      <Modal
        title="批量修改状态"
        open={batchStatusModalVisible}
        onOk={handleBatchStatusConfirm}
        onCancel={handleBatchStatusCancel}
        confirmLoading={loading}
      >
        <Alert
          message={`将为选中的 ${selectedRowKeys.length} 条记录修改状态`}
          type="info"
          style={{ marginBottom: 16 }}
        />
        <div>
          <label>选择新状态：</label>
          <Select
            style={{ width: "100%", marginTop: 8 }}
            placeholder="请选择状态"
            value={selectedStatus}
            onChange={setSelectedStatus}
            options={getSelectOptions("StoreWithdraw.Status")}
          />
        </div>
      </Modal>

      {/* 批量修改店铺Modal */}
      <Modal
        title="批量修改店铺"
        open={batchStoreModalVisible}
        onOk={handleBatchStoreConfirm}
        onCancel={handleBatchStoreCancel}
        confirmLoading={loading}
      >
        <Alert
          message={`将为选中的 ${selectedRowKeys.length} 条记录修改店铺信息`}
          type="info"
          style={{ marginBottom: 16 }}
        />
        <div>
          <label>输入新的店铺ID：</label>
          <Input
            style={{ marginTop: 8 }}
            placeholder="请输入店铺ID"
            value={selectedStoreId}
            onChange={(e) => setSelectedStoreId(e.target.value)}
          />
        </div>
      </Modal>

      {/* 批量修改钱包Modal */}
      <Modal
        title="批量修改钱包"
        open={batchWalletModalVisible}
        onOk={handleBatchWalletConfirm}
        onCancel={handleBatchWalletCancel}
        confirmLoading={loading}
      >
        <Alert
          message={`将为选中的 ${selectedRowKeys.length} 条记录修改钱包信息`}
          type="info"
          style={{ marginBottom: 16 }}
        />
        <div>
          <label>输入新的钱包ID：</label>
          <Input
            style={{ marginTop: 8 }}
            placeholder="请输入钱包ID"
            value={selectedWalletId}
            onChange={(e) => setSelectedWalletId(e.target.value)}
          />
        </div>
      </Modal>
    </Card>
  );
}

export default ShopWithdrawalPage;
