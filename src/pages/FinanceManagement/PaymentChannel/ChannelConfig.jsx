import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  Card,
  Table,
  Button,
  Space,
  Select,
  DatePicker,
  Typography,
  Row,
  Col,
  message,
  Tag,
  Form,
  Input,
  InputNumber,
  Popconfirm,
  Modal,
  Alert,
} from "antd";
import PageTabs from "@/components/PageTabs/PageTabs";
import AddChannelConfigComponent from "./AddChannelConfigComponent";
import { usePageTabs } from "@/hooks/usePageTabs";
import { useGlobalConstants } from "@/hooks/useGlobalConstants";
import { DeleteOutlined } from "@ant-design/icons";
import dayjs from "dayjs";
import {
  fetchChannelConfigList,
  fetchChannelConfigDetail,
  addChannelConfig,
  updateChannelConfig,
  deleteChannelConfig,
  batchDeleteChannelConfig,
  batchUpdateChannelConfigStatus,
  batchUpdateChannelConfigType,
  batchUpdateChannelConfigName,
  setFilters,
  setCurrentPage,
  setPageSize,
  resetState,
  clearSelectedPayment,
} from "@/redux/channelConfigPage/channelConfigPageSlice";

const { Title } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

function ChannelConfigPage() {
  const dispatch = useDispatch();
  const {
    list = [],
    total = 0,
    currentPage = 1,
    pageSize = 20,
    maxPage = 1,
    loading = false,
    error = null,
    selectedPayment = null,
    deleteData = { loading: false, error: null },
    batchDeleteData = { loading: false, error: null },
    filters = {
      status: "",
      paymentType: "",
      paymentName: "",
      createTimeStart: null,
      createTimeEnd: null,
    },
  } = useSelector((state) => state.channelConfigPage || {});

  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  // 批量操作Modal相关状态
  const [batchStatusModalVisible, setBatchStatusModalVisible] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState("");
  const [batchTypeModalVisible, setBatchTypeModalVisible] = useState(false);
  const [selectedPaymentType, setSelectedPaymentType] = useState("");
  const [batchNameModalVisible, setBatchNameModalVisible] = useState(false);
  const [selectedPaymentName, setSelectedPaymentName] = useState("");

  // 计算是否有任何删除操作正在进行，用于覆盖整个表格的加载状态
  const isDeleting = deleteData.loading || batchDeleteData.loading;

  // 获取全局枚举数据
  const {
    getSelectOptions,
    getEnumName,
    loading: enumLoading,
  } = useGlobalConstants();

  const {
    activeTab,
    editingRecord,
    tabPanes,
    handleAdd,
    handleEdit,
    handleTabChange,
    handleTabEdit,
    handleSaveSuccess,
    handleCancel,
    isListTab,
    saveTabFormData,
    getTabFormData,
    clearTabFormData,
  } = usePageTabs({
    listTabLabel: "支付渠道配置",
    tabTypes: {
      add: {
        label: "添加支付渠道",
        prefix: "add",
      },
      edit: {
        label: "编辑",
        prefix: "edit",
        getLabelFn: (record) => `编辑支付渠道 - ${record.paymentName}`,
      },
    },
    dataList: list,
    onSaveSuccess: () => {
      loadData();
    },
  });

  // 获取状态标签颜色
  const getStatusColor = (status) => {
    const statusName = getEnumName("Common.Status", status);
    switch (statusName) {
      case "启用":
        return "green";
      case "禁用":
        return "red";
      default:
        return "default";
    }
  };

  // 获取支付图标
  const getPaymentIcon = (paymentName) => {
    const iconStyle = { width: 32, height: 32, cursor: "pointer" };

    switch (paymentName) {
      case "Alipay":
        return (
          <div
            style={{
              ...iconStyle,
              backgroundColor: "#1677ff",
              borderRadius: "4px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              color: "white",
              fontSize: "12px",
            }}
          >
            支付宝
          </div>
        );
      case "Bitcoin":
        return (
          <div
            style={{
              ...iconStyle,
              backgroundColor: "#f7931a",
              borderRadius: "4px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              color: "white",
              fontSize: "12px",
            }}
          >
            BTC
          </div>
        );
      case "MasterCard":
        return (
          <div
            style={{
              ...iconStyle,
              backgroundColor: "#eb001b",
              borderRadius: "4px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              color: "white",
              fontSize: "10px",
            }}
          >
            MC
          </div>
        );
      case "WeChat Pay":
        return (
          <div
            style={{
              ...iconStyle,
              backgroundColor: "#07c160",
              borderRadius: "4px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              color: "white",
              fontSize: "12px",
            }}
          >
            微信
          </div>
        );
      case "Visa":
        return (
          <div
            style={{
              ...iconStyle,
              backgroundColor: "#1a1f71",
              borderRadius: "4px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              color: "white",
              fontSize: "12px",
            }}
          >
            VISA
          </div>
        );
      case "PayPal":
        return (
          <div
            style={{
              ...iconStyle,
              backgroundColor: "#0070ba",
              borderRadius: "4px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              color: "white",
              fontSize: "10px",
            }}
          >
            PayPal
          </div>
        );
      case "UnionPay":
        return (
          <div
            style={{
              ...iconStyle,
              backgroundColor: "#e21836",
              borderRadius: "4px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              color: "white",
              fontSize: "10px",
            }}
          >
            银联
          </div>
        );
      default:
        return (
          <div
            style={{
              ...iconStyle,
              backgroundColor: "#d9d9d9",
              borderRadius: "4px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              color: "#666",
              fontSize: "10px",
            }}
          >
            支付
          </div>
        );
    }
  };

  // 表格列定义
  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      width: 60,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      width: 100,
      render: (status) => {
        const statusName = getEnumName("Common.Status", status);
        return <Tag color={getStatusColor(status)}>{statusName || status}</Tag>;
      },
    },
    {
      title: "支付名称",
      dataIndex: "paymentName",
      key: "paymentName",
      width: 120,
    },
    {
      title: "支付类型",
      dataIndex: "paymentType",
      key: "paymentType",
      width: 140,
    },
    {
      title: "支付标志",
      dataIndex: "paymentLogo",
      key: "paymentLogo",
      width: 100,
      render: (_, record) => getPaymentIcon(record.paymentName),
    },
    {
      title: "日交易额",
      dataIndex: "dayAmount",
      key: "dayAmount",
      width: 120,
      sorter: (a, b) => a.dayAmount - b.dayAmount,
      render: (amount) => (amount ? amount.toFixed(2) : "0.00"),
    },
    {
      title: "月交易额",
      dataIndex: "monthAmount",
      key: "monthAmount",
      width: 120,
      sorter: (a, b) => a.monthAmount - b.monthAmount,
      render: (amount) => (amount ? amount.toFixed(2) : "0.00"),
    },
    {
      title: "佣金百分比",
      dataIndex: "commissionPercent",
      key: "commissionPercent",
      width: 120,
      render: (commission) =>
        commission ? `${(commission / 100).toFixed(2)}%` : "0.00%",
    },
    {
      title: "创建时间",
      dataIndex: "createTime",
      key: "createTime",
      width: 160,
      sorter: (a, b) => a.createTime - b.createTime,
      render: (createTime) =>
        createTime ? dayjs.unix(createTime).format("YYYY-MM-DD HH:mm:ss") : "-",
    },
    {
      title: "操作",
      key: "action",
      width: 150,
      fixed: "right",
      render: (_, record) => (
        <Space size="small">
          <Button
            size="small"
            onClick={() => handleEdit(record)}
            style={{
              backgroundColor: "#ff7a00",
              borderColor: "#ff7a00",
              color: "white",
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个支付渠道吗？"
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button size="small" danger loading={deleteData.loading}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedKeys) => {
      setSelectedRowKeys(selectedKeys);
    },
  };

  // 加载数据
  const loadData = () => {
    dispatch(
      fetchChannelConfigList({
        page: currentPage,
        pageSize: pageSize,
        filters: filters,
      })
    );
  };

  // 组件挂载时加载数据
  useEffect(() => {
    loadData();
    return () => {
      dispatch(resetState());
    };
  }, []);

  // 处理删除
  const handleDelete = async (record) => {
    try {
      await dispatch(deleteChannelConfig(record.id)).unwrap();
      message.success("删除支付渠道成功");
      loadData();
    } catch (error) {
      message.error("删除支付渠道失败");
    }
  };

  // 处理批量删除
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请选择要删除的支付渠道");
      return;
    }
    try {
      await dispatch(batchDeleteChannelConfig(selectedRowKeys)).unwrap();
      message.success(`成功删除 ${selectedRowKeys.length} 个支付渠道`);
      setSelectedRowKeys([]);
      loadData();
    } catch (error) {
      message.error("批量删除支付渠道失败");
    }
  };

  // 处理批量修改状态
  const handleBatchModifyStatus = () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请选择要修改状态的支付渠道");
      return;
    }
    setBatchStatusModalVisible(true);
    setSelectedStatus("");
  };

  const handleBatchStatusConfirm = async () => {
    if (!selectedStatus) {
      message.warning("请选择状态");
      return;
    }

    try {
      await dispatch(
        batchUpdateChannelConfigStatus({
          ids: selectedRowKeys,
          status: selectedStatus,
        })
      ).unwrap();
      message.success(`成功修改 ${selectedRowKeys.length} 个支付渠道的状态`);
      setSelectedRowKeys([]);
      loadData();
      setBatchStatusModalVisible(false);
    } catch (error) {
      message.error("批量修改状态失败");
    }
  };

  const handleBatchStatusCancel = () => {
    setBatchStatusModalVisible(false);
    setSelectedStatus("");
  };

  // 处理批量修改支付类型
  const handleBatchModifyType = () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请选择要修改类型的支付渠道");
      return;
    }
    setBatchTypeModalVisible(true);
    setSelectedPaymentType("");
  };

  const handleBatchTypeConfirm = async () => {
    if (!selectedPaymentType) {
      message.warning("请选择支付类型");
      return;
    }

    try {
      await dispatch(
        batchUpdateChannelConfigType({
          ids: selectedRowKeys,
          paymentType: selectedPaymentType,
        })
      ).unwrap();
      message.success(`成功修改 ${selectedRowKeys.length} 个支付渠道的类型`);
      setSelectedRowKeys([]);
      loadData();
      setBatchTypeModalVisible(false);
    } catch (error) {
      message.error("批量修改类型失败");
    }
  };

  const handleBatchTypeCancel = () => {
    setBatchTypeModalVisible(false);
    setSelectedPaymentType("");
  };

  // 处理批量修改支付名称
  const handleBatchModifyName = () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请选择要修改名称的支付渠道");
      return;
    }
    setBatchNameModalVisible(true);
    setSelectedPaymentName("");
  };

  const handleBatchNameConfirm = async () => {
    if (!selectedPaymentName) {
      message.warning("请选择支付名称");
      return;
    }

    try {
      await dispatch(
        batchUpdateChannelConfigName({
          ids: selectedRowKeys,
          paymentName: selectedPaymentName,
        })
      ).unwrap();
      message.success(`成功修改 ${selectedRowKeys.length} 个支付渠道的名称`);
      setSelectedRowKeys([]);
      loadData();
      setBatchNameModalVisible(false);
    } catch (error) {
      message.error("批量修改名称失败");
    }
  };

  const handleBatchNameCancel = () => {
    setBatchNameModalVisible(false);
    setSelectedPaymentName("");
  };

  // 处理查询
  const handleSearch = () => {
    dispatch(setCurrentPage(1));
    // 延迟一下让页码状态更新后再加载数据
    setTimeout(() => {
      loadData();
    }, 0);
  };

  // 处理筛选条件变化
  const handleFilterChange = (key, value) => {
    dispatch(setFilters({ [key]: value }));
  };

  // 处理日期范围变化
  const handleDateRangeChange = (dates) => {
    // 确保数据格式的一致性（始终为数组或null）
    if (dates && dates.length === 2) {
      dispatch(
        setFilters({
          createTimeStart: dates[0],
          createTimeEnd: dates[1],
        })
      );
    } else {
      dispatch(
        setFilters({
          createTimeStart: null,
          createTimeEnd: null,
        })
      );
    }
  };

  // 处理分页变化
  const handleTableChange = (pagination) => {
    dispatch(setCurrentPage(pagination.current));
    dispatch(setPageSize(pagination.pageSize));
    // 延迟一下让状态更新后再加载数据
    setTimeout(() => {
      loadData();
    }, 0);
  };

  // 处理重置
  const handleReset = () => {
    dispatch(
      setFilters({
        status: "",
        paymentType: "",
        paymentName: "",
        createTimeStart: null,
        createTimeEnd: null,
      })
    );
    dispatch(setCurrentPage(1));
    loadData();
    message.success("筛选条件已重置");
  };

  return (
    <Card style={{ backgroundColor: "#fff" }}>
      {/* 页面标签栏 */}
      <div className="page-tabs-wrapper">
        <PageTabs
          activeKey={activeTab}
          onChange={handleTabChange}
          onEdit={handleTabEdit}
          items={tabPanes}
          type="editable-card"
        />
      </div>

      {/* 条件渲染 */}
      {isListTab ? (
        <>
          {/* 筛选条件 */}
          <Card style={{ marginBottom: 16 }}>
            <Row gutter={[16, 16]} align="middle">
              <Col>
                <Select
                  value={filters.status || "全部状态"}
                  onChange={(value) =>
                    handleFilterChange(
                      "status",
                      value === "全部状态" ? "" : value
                    )
                  }
                  style={{ width: 120 }}
                  loading={enumLoading}
                >
                  <Option value="全部状态">全部状态</Option>
                  {getSelectOptions("Common.Status").map((option) => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Col>
              <Col>
                <Select
                  value={filters.paymentType || "全部类型"}
                  onChange={(value) =>
                    handleFilterChange(
                      "paymentType",
                      value === "全部类型" ? "" : value
                    )
                  }
                  style={{ width: 120 }}
                >
                  <Option value="全部类型">全部类型</Option>
                  <Option value="Credit Card">Credit Card</Option>
                  <Option value="Cryptocurrency">Cryptocurrency</Option>
                  <Option value="Bank Transfer">Bank Transfer</Option>
                  <Option value="Mobile Payment">Mobile Payment</Option>
                  <Option value="Cash">Cash</Option>
                </Select>
              </Col>
              <Col>
                <Select
                  value={filters.paymentName || "全部名称"}
                  onChange={(value) =>
                    handleFilterChange(
                      "paymentName",
                      value === "全部名称" ? "" : value
                    )
                  }
                  style={{ width: 120 }}
                >
                  <Option value="全部名称">全部名称</Option>
                  <Option value="Alipay">Alipay</Option>
                  <Option value="Bitcoin">Bitcoin</Option>
                  <Option value="MasterCard">MasterCard</Option>
                  <Option value="WeChat Pay">WeChat Pay</Option>
                  <Option value="Visa">Visa</Option>
                  <Option value="PayPal">PayPal</Option>
                  <Option value="UnionPay">UnionPay</Option>
                </Select>
              </Col>
              <Col>
                <span>年/月/日</span>
              </Col>
              <Col>
                <RangePicker
                  value={
                    filters.createTimeStart && filters.createTimeEnd
                      ? [
                          dayjs(filters.createTimeStart),
                          dayjs(filters.createTimeEnd),
                        ]
                      : [null, null]
                  }
                  onChange={handleDateRangeChange}
                  style={{ width: 240 }}
                />
              </Col>
              <Col>
                <Button type="primary" onClick={handleSearch}>
                  查询
                </Button>
              </Col>
              <Col>
                <Button onClick={handleReset}>重置</Button>
              </Col>
            </Row>
          </Card>

          {/* 批量操作按钮 */}
          <Card style={{ marginBottom: 16 }}>
            <Space wrap>
              <Button
                onClick={handleAdd}
                style={{
                  backgroundColor: "#ff7a00",
                  borderColor: "#ff7a00",
                  color: "white",
                }}
              >
                添加
              </Button>
              <Popconfirm
                title="确定要删除选中的支付渠道吗？"
                onConfirm={handleBatchDelete}
                okText="确定"
                cancelText="取消"
              >
                <Button
                  danger
                  icon={<DeleteOutlined />}
                  disabled={selectedRowKeys.length === 0}
                  loading={batchDeleteData.loading}
                >
                  批量删除
                </Button>
              </Popconfirm>
              <Button
                onClick={handleBatchModifyStatus}
                disabled={selectedRowKeys.length === 0}
                style={{
                  backgroundColor: "#ff7a00",
                  borderColor: "#ff7a00",
                  color: "white",
                }}
              >
                批量修改状态
              </Button>
              <Button
                onClick={handleBatchModifyType}
                disabled={selectedRowKeys.length === 0}
                style={{
                  backgroundColor: "#ff7a00",
                  borderColor: "#ff7a00",
                  color: "white",
                }}
              >
                批量修改支付类型
              </Button>
              <Button
                onClick={handleBatchModifyName}
                disabled={selectedRowKeys.length === 0}
                style={{
                  backgroundColor: "#ff7a00",
                  borderColor: "#ff7a00",
                  color: "white",
                }}
              >
                批量修改支付名称
              </Button>
            </Space>
          </Card>

          {/* 数据表格 */}
          <Card>
            <Table
              columns={columns}
              dataSource={
                Array.isArray(list)
                  ? list.map((item) => ({ ...item, key: item.id }))
                  : []
              }
              loading={loading || isDeleting}
              rowSelection={rowSelection}
              pagination={{
                current: currentPage,
                pageSize: pageSize,
                total: total,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `第 ${range[0]} 页 / 共 ${Math.ceil(total / pageSize)} 页`,
                pageSizeOptions: ["10", "20", "50"],
                size: "small",
              }}
              onChange={handleTableChange}
              scroll={{ x: 1400 }}
              size="middle"
              bordered
            />
          </Card>
        </>
      ) : (
        /* 新的添加/编辑组件 */
        <AddChannelConfigComponent
          editingRecord={activeTab.startsWith("add-") ? null : editingRecord}
          onSave={handleSaveSuccess}
          onCancel={handleCancel}
          tabKey={activeTab}
          saveTabFormData={saveTabFormData}
          getTabFormData={getTabFormData}
          clearTabFormData={clearTabFormData}
        />
      )}

      {/* 批量修改状态Modal */}
      <Modal
        title="批量修改状态"
        open={batchStatusModalVisible}
        onOk={handleBatchStatusConfirm}
        onCancel={handleBatchStatusCancel}
        confirmLoading={loading}
      >
        <Alert
          message={`将为选中的 ${selectedRowKeys.length} 条记录修改状态`}
          type="info"
          style={{ marginBottom: 16 }}
        />
        <div>
          <label>选择新状态：</label>
          <Select
            style={{ width: "100%", marginTop: 8 }}
            placeholder="请选择状态"
            value={selectedStatus}
            onChange={setSelectedStatus}
            options={[
              { label: "启用", value: "1" },
              { label: "禁用", value: "0" },
            ]}
          />
        </div>
      </Modal>

      {/* 批量修改支付类型Modal */}
      <Modal
        title="批量修改支付类型"
        open={batchTypeModalVisible}
        onOk={handleBatchTypeConfirm}
        onCancel={handleBatchTypeCancel}
        confirmLoading={loading}
      >
        <Alert
          message={`将为选中的 ${selectedRowKeys.length} 条记录修改支付类型`}
          type="info"
          style={{ marginBottom: 16 }}
        />
        <div>
          <label>选择新的支付类型：</label>
          <Select
            style={{ width: "100%", marginTop: 8 }}
            placeholder="请选择支付类型"
            value={selectedPaymentType}
            onChange={setSelectedPaymentType}
            options={[
              { label: "Credit Card", value: "Credit Card" },
              { label: "Cryptocurrency", value: "Cryptocurrency" },
              { label: "Bank Transfer", value: "Bank Transfer" },
              { label: "Mobile Payment", value: "Mobile Payment" },
              { label: "Cash", value: "Cash" },
            ]}
          />
        </div>
      </Modal>

      {/* 批量修改支付名称Modal */}
      <Modal
        title="批量修改支付名称"
        open={batchNameModalVisible}
        onOk={handleBatchNameConfirm}
        onCancel={handleBatchNameCancel}
        confirmLoading={loading}
      >
        <Alert
          message={`将为选中的 ${selectedRowKeys.length} 条记录修改支付名称`}
          type="info"
          style={{ marginBottom: 16 }}
        />
        <div>
          <label>选择新的支付名称：</label>
          <Select
            style={{ width: "100%", marginTop: 8 }}
            placeholder="请选择支付名称"
            value={selectedPaymentName}
            onChange={setSelectedPaymentName}
            options={[
              { label: "Alipay", value: "Alipay" },
              { label: "Bitcoin", value: "Bitcoin" },
              { label: "MasterCard", value: "MasterCard" },
              { label: "WeChat Pay", value: "WeChat Pay" },
              { label: "Visa", value: "Visa" },
              { label: "PayPal", value: "PayPal" },
              { label: "UnionPay", value: "UnionPay" },
            ]}
          />
        </div>
      </Modal>
    </Card>
  );
}

export default ChannelConfigPage;
