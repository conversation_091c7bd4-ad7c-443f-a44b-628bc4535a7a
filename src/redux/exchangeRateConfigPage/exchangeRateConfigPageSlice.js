import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import exchangeRateConfigService from "@/services/exchangeRateConfigService";

// 异步thunk actions
export const fetchExchangeRateList = createAsyncThunk(
  "exchangeRateConfigPage/fetchList",
  async (queryRequest, { rejectWithValue }) => {
    try {
      const response = await exchangeRateConfigService.getList(queryRequest);
      return response;
    } catch (error) {
      return rejectWithValue(error.message || "获取汇率配置列表失败");
    }
  }
);

export const addExchangeRate = createAsyncThunk(
  "exchangeRateConfigPage/add",
  async (requestData, { rejectWithValue }) => {
    try {
      const response = await exchangeRateConfigService.add(requestData);
      return response;
    } catch (error) {
      return rejectWithValue(error.message || "添加汇率配置失败");
    }
  }
);

export const updateExchangeRate = createAsyncThunk(
  "exchangeRateConfigPage/update",
  async ({ id, requestData }, { rejectWithValue }) => {
    try {
      const response = await exchangeRateConfigService.update(id, requestData);
      return response;
    } catch (error) {
      return rejectWithValue(error.message || "更新汇率配置失败");
    }
  }
);

export const getExchangeRateDetail = createAsyncThunk(
  "exchangeRateConfigPage/getDetail",
  async (id, { rejectWithValue }) => {
    try {
      const response = await exchangeRateConfigService.getDetail(id);
      return response;
    } catch (error) {
      return rejectWithValue(error.message || "获取汇率配置详情失败");
    }
  }
);

export const deleteExchangeRate = createAsyncThunk(
  "exchangeRateConfigPage/delete",
  async (id, { rejectWithValue }) => {
    try {
      const response = await exchangeRateConfigService.delete(id);
      return response;
    } catch (error) {
      return rejectWithValue(error.message || "删除汇率配置失败");
    }
  }
);

export const batchDeleteExchangeRate = createAsyncThunk(
  "exchangeRateConfigPage/batchDelete",
  async (ids, { rejectWithValue }) => {
    try {
      const response = await exchangeRateConfigService.batchDelete(ids);
      return response;
    } catch (error) {
      return rejectWithValue(error.message || "批量删除汇率配置失败");
    }
  }
);

export const batchUpdateCurrency = createAsyncThunk(
  "exchangeRateConfigPage/batchUpdateCurrency",
  async ({ ids, updateDTO }, { rejectWithValue }) => {
    try {
      const response = await exchangeRateConfigService.batchUpdateCurrency(
        ids,
        updateDTO
      );
      return response;
    } catch (error) {
      return rejectWithValue(error.message || "批量更新货币代码失败");
    }
  }
);

// 初始状态
const initialState = {
  list: [],
  total: 0,
  currentPage: 1,
  pageSize: 20,
  maxPage: 1,
  loading: false,
  error: null,
  selectedRateConfig: null,
  deleteData: {
    loading: false,
    error: null,
  },
  batchDeleteData: {
    loading: false,
    error: null,
  },
  filters: {
    currency: "",
    currencyName: "",
    createTimeStart: null,
    createTimeEnd: null,
  },
};

// slice
const exchangeRateConfigPageSlice = createSlice({
  name: "exchangeRateConfigPage",
  initialState,
  reducers: {
    // 更新筛选条件
    updateFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    // 更新分页信息
    updatePagination: (state, action) => {
      const { currentPage, pageSize } = action.payload;
      if (currentPage !== undefined) state.currentPage = currentPage;
      if (pageSize !== undefined) state.pageSize = pageSize;
    },
    // 重置状态
    resetState: (state) => {
      return initialState;
    },
    // 清除错误
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    // 获取列表
    builder
      .addCase(fetchExchangeRateList.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchExchangeRateList.fulfilled, (state, action) => {
        state.loading = false;
        const { data } = action.payload;
        state.list = data.data || [];
        state.total = data.total || 0;
        state.currentPage = data.curPage || 1;
        state.maxPage = data.maxPage || 1;
      })
      .addCase(fetchExchangeRateList.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });

    // 添加汇率配置
    builder
      .addCase(addExchangeRate.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(addExchangeRate.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(addExchangeRate.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });

    // 更新汇率配置
    builder
      .addCase(updateExchangeRate.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateExchangeRate.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(updateExchangeRate.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });

    // 获取详情
    builder
      .addCase(getExchangeRateDetail.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getExchangeRateDetail.fulfilled, (state, action) => {
        state.loading = false;
        state.selectedRateConfig = action.payload;
      })
      .addCase(getExchangeRateDetail.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });

    // 删除汇率配置
    builder
      .addCase(deleteExchangeRate.pending, (state) => {
        state.deleteData.loading = true;
        state.deleteData.error = null;
      })
      .addCase(deleteExchangeRate.fulfilled, (state) => {
        state.deleteData.loading = false;
      })
      .addCase(deleteExchangeRate.rejected, (state, action) => {
        state.deleteData.loading = false;
        state.deleteData.error = action.payload;
      });

    // 批量删除
    builder
      .addCase(batchDeleteExchangeRate.pending, (state) => {
        state.batchDeleteData.loading = true;
        state.batchDeleteData.error = null;
      })
      .addCase(batchDeleteExchangeRate.fulfilled, (state) => {
        state.batchDeleteData.loading = false;
      })
      .addCase(batchDeleteExchangeRate.rejected, (state, action) => {
        state.batchDeleteData.loading = false;
        state.batchDeleteData.error = action.payload;
      });

    // 批量更新汇率
    builder
      .addCase(batchUpdateCurrency.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(batchUpdateCurrency.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(batchUpdateCurrency.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const { updateFilters, updatePagination, resetState, clearError } =
  exchangeRateConfigPageSlice.actions;

export default exchangeRateConfigPageSlice.reducer;
