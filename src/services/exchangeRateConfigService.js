import axios from "@/api/axios";
import dayjs from "dayjs";

const API_BASE_URL = "/rate/config";

const exchangeRateConfigService = {
  // 获取汇率配置列表
  getList: async (queryRequest = {}) => {
    const params = {
      page: queryRequest.page || 1,
      pageSize: queryRequest.pageSize || 20,
    };

    // 添加筛选条件
    if (queryRequest.currency) {
      params.currency = queryRequest.currency;
    }
    if (queryRequest.currencyName) {
      params.currencyName = queryRequest.currencyName;
    }

    // 处理日期参数 - 转换为秒级时间戳
    if (queryRequest.createTimeStart) {
      params.createTimeStart = Math.floor(
        dayjs(queryRequest.createTimeStart).valueOf() / 1000
      );
    }
    if (queryRequest.createTimeEnd) {
      params.createTimeEnd = Math.floor(
        dayjs(queryRequest.createTimeEnd).valueOf() / 1000
      );
    }

    return await axios.get(`${API_BASE_URL}/list`, { params });
  },

  // 添加汇率配置
  add: async (requestData) => {
    return await axios.post(`${API_BASE_URL}`, requestData);
  },

  // 更新汇率配置
  update: async (id, requestData) => {
    return await axios.put(`${API_BASE_URL}/${id}`, requestData);
  },

  // 获取汇率配置详情
  getDetail: async (id) => {
    return await axios.get(`${API_BASE_URL}/${id}`);
  },

  // 删除汇率配置
  delete: async (id) => {
    return await axios.delete(`${API_BASE_URL}/${id}`);
  },

  // 批量删除汇率配置
  batchDelete: async (ids) => {
    return await axios.delete(`${API_BASE_URL}/batch`, { params: { ids } });
  },

  // 批量更新货币代码
  batchUpdateCurrency: async (ids, updateDTO) => {
    return await axios.put(`${API_BASE_URL}/batch/currency`, null, {
      params: {
        ids,
        currency: updateDTO.currency,
      },
    });
  },
};

export default exchangeRateConfigService;
