import axios from "@/api/axios";
import dayjs from "dayjs";

const API_BASE = "/store/withdrawals";

/**
 * 获取店铺提现列表
 * @param {Object} queryRequest - 查询参数
 * @returns {Promise} API响应
 */
export const getWithdrawalList = async (queryRequest) => {
  const params = {
    page: queryRequest.page || 1,
    pageSize: queryRequest.pageSize || 20,
  };

  // 处理筛选条件
  if (queryRequest.storeId) {
    params.storeId = queryRequest.storeId;
  }

  if (queryRequest.userId) {
    params.userId = queryRequest.userId;
  }

  if (queryRequest.userWalletId) {
    params.userWalletId = queryRequest.userWalletId;
  }

  if (queryRequest.status) {
    params.status = queryRequest.status;
  }

  // 处理日期范围 - 转换为秒级时间戳
  if (queryRequest.createTimeStart) {
    params.createTimeStart = Math.floor(
      dayjs(queryRequest.createTimeStart).valueOf() / 1000
    );
  }

  if (queryRequest.createTimeEnd) {
    params.createTimeEnd = Math.floor(
      dayjs(queryRequest.createTimeEnd).valueOf() / 1000
    );
  }

  const response = await axios.get(`${API_BASE}/list`, { params });
  return response;
};

/**
 * 获取店铺提现详情
 * @param {number} id - 提现记录ID
 * @returns {Promise} API响应
 */
export const getWithdrawalDetails = async (id) => {
  const response = await axios.get(`${API_BASE}/${id}`);
  return response;
};

/**
 * 添加店铺提现记录
 * @param {Object} withdrawalData - 提现数据
 * @returns {Promise} API响应
 */
export const addWithdrawal = async (withdrawalData) => {
  // 处理日期字段 - 转换为秒级时间戳
  const processedData = { ...withdrawalData };

  if (processedData.finishedTime) {
    processedData.finishedTime = Math.floor(
      dayjs(processedData.finishedTime).valueOf() / 1000
    );
  }

  const response = await axios.post(`${API_BASE}`, processedData);
  return response;
};

/**
 * 更新店铺提现记录
 * @param {number} id - 提现记录ID
 * @param {Object} withdrawalData - 提现数据
 * @returns {Promise} API响应
 */
export const updateWithdrawal = async (id, withdrawalData) => {
  // 处理日期字段 - 转换为秒级时间戳
  const processedData = { ...withdrawalData };

  if (processedData.finishedTime) {
    processedData.finishedTime = Math.floor(
      dayjs(processedData.finishedTime).valueOf() / 1000
    );
  }

  const response = await axios.put(`${API_BASE}/${id}`, processedData);
  return response;
};

/**
 * 删除店铺提现记录
 * @param {number} id - 提现记录ID
 * @returns {Promise} API响应
 */
export const deleteWithdrawal = async (id) => {
  const response = await axios.delete(`${API_BASE}/${id}`);
  return response;
};

/**
 * 批量删除店铺提现记录
 * @param {Array<number>} ids - 提现记录ID数组
 * @returns {Promise} API响应
 */
export const batchDeleteWithdrawals = async (ids) => {
  const response = await axios.delete(`${API_BASE}/batch`, { params: { ids } });
  return response;
};

/**
 * 批量更新提现状态
 * @param {Array<number>} ids - 提现记录ID数组
 * @param {string} status - 状态值
 * @returns {Promise} API响应
 */
export const batchUpdateStatus = async (ids, status) => {
  const response = await axios.put(`${API_BASE}/batch/status`, null, {
    params: { ids, status },
  });
  return response;
};

/**
 * 批量更新店铺
 * @param {Array<number>} ids - 提现记录ID数组
 * @param {number} storeId - 店铺ID
 * @returns {Promise} API响应
 */
export const batchUpdateStore = async (ids, storeId) => {
  const response = await axios.put(`${API_BASE}/batch/store`, null, {
    params: { ids, storeId },
  });
  return response;
};

/**
 * 批量更新钱包
 * @param {Array<number>} ids - 提现记录ID数组
 * @param {number} walletId - 钱包ID
 * @returns {Promise} API响应
 */
export const batchUpdateWallet = async (ids, walletId) => {
  const response = await axios.put(`${API_BASE}/batch/wallet`, null, {
    params: { ids, walletId },
  });
  return response;
};
