// 全局枚举配置管理工具类
class GlobalConstantsManager {
  constructor() {
    this.STORAGE_KEY = 'global_constants';
    this.constants = {};
    this.lastUpdated = null;
    this.initialized = false;
  }

  // 初始化 - 从 localStorage 加载数据
  init() {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        const data = JSON.parse(stored);
        this.constants = data.constants || {};
        this.lastUpdated = data.lastUpdated ? new Date(data.lastUpdated) : null;
        this.initialized = true;
        console.log('全局枚举配置已从缓存加载:', this.constants);
      }
    } catch (error) {
      console.error('加载全局枚举配置缓存失败:', error);
      this.clearCache();
    }
  }

  // 更新数据并保存到 localStorage
  updateConstants(newConstants) {
    try {
      this.constants = newConstants || {};
      this.lastUpdated = new Date();
      this.initialized = true;

      const dataToStore = {
        constants: this.constants,
        lastUpdated: this.lastUpdated.toISOString()
      };

      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(dataToStore));
      console.log('全局枚举配置已更新并保存到缓存:', this.constants);
    } catch (error) {
      console.error('保存全局枚举配置到缓存失败:', error);
    }
  }

  // 清除缓存
  clearCache() {
    this.constants = {};
    this.lastUpdated = null;
    this.initialized = false;
    localStorage.removeItem(this.STORAGE_KEY);
  }

  // 获取指定枚举的所有选项
  getEnumOptions(enumKey) {
    if (!this.constants || !enumKey) {
      return [];
    }
    return this.constants[enumKey] || [];
  }

  // 根据 id 获取枚举项
  getEnumById(enumKey, id) {
    const options = this.getEnumOptions(enumKey);
    return options.find(item => item.id === id) || null;
  }

  // 根据 key 获取枚举项
  getEnumByKey(enumKey, key) {
    const options = this.getEnumOptions(enumKey);
    return options.find(item => item.key === key) || null;
  }

  // 获取枚举项的显示名称
  getEnumName(enumKey, idOrKey) {
    if (idOrKey === null || idOrKey === undefined) {
      return '';
    }

    // 先尝试按 id 查找
    let item = this.getEnumById(enumKey, idOrKey);

    // 如果没找到，再尝试按 key 查找
    if (!item) {
      item = this.getEnumByKey(enumKey, idOrKey);
    }

    if (!item) {
      return '';
    }

    // 特殊处理：Locale.Language 枚举显示使用 key 字段
    if (enumKey === 'Locale.Language') {
      return item.key;
    }

    // 其他枚举使用 name 字段
    return item.name;
  }

  // 检查枚举值是否存在
  hasEnum(enumKey, idOrKey) {
    return this.getEnumById(enumKey, idOrKey) !== null ||
      this.getEnumByKey(enumKey, idOrKey) !== null;
  }

  // 获取枚举项的 key 值
  getEnumKey(enumKey, idOrKey) {
    if (idOrKey === null || idOrKey === undefined) {
      return '';
    }

    // 先尝试按 id 查找
    let item = this.getEnumById(enumKey, idOrKey);

    // 如果没找到，再尝试按 key 查找
    if (!item) {
      item = this.getEnumByKey(enumKey, idOrKey);
    }

    return item ? item.key : '';
  }

  // 获取枚举的键值对映射 (key -> name)
  getEnumMap(enumKey) {
    const options = this.getEnumOptions(enumKey);
    const map = {};
    options.forEach(item => {
      map[item.key] = item.name;
    });
    return map;
  }

  // 获取枚举的 ID 值对映射 (id -> name)
  getEnumIdMap(enumKey) {
    const options = this.getEnumOptions(enumKey);
    const map = {};
    options.forEach(item => {
      map[item.id] = item.name;
    });
    return map;
  }

  // 获取适用于 Ant Design Select 的选项 (使用 id 作为 value)
  getSelectOptions(enumKey) {
    const options = this.getEnumOptions(enumKey);
    return options.map(item => ({
      label: item.name,
      value: item.id
    }));
  }

  // 获取适用于 Ant Design Select 的选项 (使用 key 作为 value)
  getSelectOptionsByKey(enumKey) {
    const options = this.getEnumOptions(enumKey);
    return options.map(item => ({
      // 特殊处理：Locale.Language 枚举显示使用 key 字段
      label: enumKey === 'Locale.Language' ? item.key : item.name,
      value: item.key
    }));
  }

  // 获取所有枚举配置
  getAllConstants() {
    return this.constants;
  }

  // 获取所有枚举的键名
  getEnumKeys() {
    return Object.keys(this.constants);
  }

  // 检查是否已初始化
  isInitialized() {
    return this.initialized;
  }

  // 获取最后更新时间
  getLastUpdated() {
    return this.lastUpdated;
  }
}

// 创建单例实例
const globalConstantsManager = new GlobalConstantsManager();

export default globalConstantsManager;
