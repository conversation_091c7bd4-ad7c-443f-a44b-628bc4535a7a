<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Locale.Language 枚举修复测试</title>
</head>
<body>
    <h1>Locale.Language 枚举修复测试</h1>
    
    <div id="test-results">
        <h2>测试结果：</h2>
        <div id="results"></div>
    </div>

    <script>
        // 模拟 Locale.Language 枚举数据
        const mockLocaleLanguageEnum = [
            { id: 1, key: "zh_CN", name: "中文简体" },
            { id: 2, key: "en_US", name: "English" },
            { id: 3, key: "ja_<PERSON>", name: "日本語" }
        ];

        // 模拟 globalConstantsManager 的修复后方法
        class MockGlobalConstantsManager {
            constructor() {
                this.constants = {
                    'Locale.Language': mockLocaleLanguageEnum,
                    'User.Status': [
                        { id: 1, key: "ACTIVE", name: "激活" },
                        { id: 2, key: "INACTIVE", name: "未激活" }
                    ]
                };
            }

            getEnumOptions(enumKey) {
                return this.constants[enumKey] || [];
            }

            getEnumById(enumKey, id) {
                const options = this.getEnumOptions(enumKey);
                return options.find(item => item.id === id) || null;
            }

            getEnumByKey(enumKey, key) {
                const options = this.getEnumOptions(enumKey);
                return options.find(item => item.key === key) || null;
            }

            // 修复后的 getEnumName 方法
            getEnumName(enumKey, idOrKey) {
                if (idOrKey === null || idOrKey === undefined) {
                    return '';
                }

                let item = this.getEnumById(enumKey, idOrKey);
                if (!item) {
                    item = this.getEnumByKey(enumKey, idOrKey);
                }

                if (!item) {
                    return '';
                }

                // 特殊处理：Locale.Language 枚举显示使用 key 字段
                if (enumKey === 'Locale.Language') {
                    return item.key;
                }

                // 其他枚举使用 name 字段
                return item.name;
            }

            // 修复后的 getSelectOptionsByKey 方法
            getSelectOptionsByKey(enumKey) {
                const options = this.getEnumOptions(enumKey);
                return options.map(item => ({
                    // 特殊处理：Locale.Language 枚举显示使用 key 字段
                    label: enumKey === 'Locale.Language' ? item.key : item.name,
                    value: item.key
                }));
            }

            getEnumKey(enumKey, idOrKey) {
                if (idOrKey === null || idOrKey === undefined) {
                    return '';
                }

                let item = this.getEnumById(enumKey, idOrKey);
                if (!item) {
                    item = this.getEnumByKey(enumKey, idOrKey);
                }

                return item ? item.key : '';
            }
        }

        // 创建测试实例
        const manager = new MockGlobalConstantsManager();

        // 测试函数
        function runTests() {
            const results = [];

            // 测试 1: Locale.Language 的 getEnumName 应该返回 key
            const langName1 = manager.getEnumName('Locale.Language', 1); // 通过 id 查找
            const langName2 = manager.getEnumName('Locale.Language', 'zh_CN'); // 通过 key 查找
            results.push(`测试 1 - Locale.Language getEnumName(id=1): ${langName1} (期望: zh_CN)`);
            results.push(`测试 1 - Locale.Language getEnumName(key='zh_CN'): ${langName2} (期望: zh_CN)`);

            // 测试 2: 其他枚举的 getEnumName 应该返回 name
            const userStatusName = manager.getEnumName('User.Status', 1);
            results.push(`测试 2 - User.Status getEnumName(id=1): ${userStatusName} (期望: 激活)`);

            // 测试 3: Locale.Language 的 getSelectOptionsByKey 应该使用 key 作为 label
            const langOptions = manager.getSelectOptionsByKey('Locale.Language');
            results.push(`测试 3 - Locale.Language getSelectOptionsByKey:`);
            langOptions.forEach(option => {
                results.push(`  - label: ${option.label}, value: ${option.value}`);
            });
            results.push(`  期望: label 和 value 都是 key 值 (zh_CN, en_US, ja_JP)`);

            // 测试 4: 其他枚举的 getSelectOptionsByKey 应该使用 name 作为 label
            const userStatusOptions = manager.getSelectOptionsByKey('User.Status');
            results.push(`测试 4 - User.Status getSelectOptionsByKey:`);
            userStatusOptions.forEach(option => {
                results.push(`  - label: ${option.label}, value: ${option.value}`);
            });
            results.push(`  期望: label 是 name (激活, 未激活), value 是 key (ACTIVE, INACTIVE)`);

            // 测试 5: getEnumKey 方法
            const langKey = manager.getEnumKey('Locale.Language', 1);
            const userStatusKey = manager.getEnumKey('User.Status', 1);
            results.push(`测试 5 - getEnumKey('Locale.Language', 1): ${langKey} (期望: zh_CN)`);
            results.push(`测试 5 - getEnumKey('User.Status', 1): ${userStatusKey} (期望: ACTIVE)`);

            return results;
        }

        // 运行测试并显示结果
        const testResults = runTests();
        const resultsDiv = document.getElementById('results');
        resultsDiv.innerHTML = testResults.map(result => `<p>${result}</p>`).join('');

        console.log('测试结果:', testResults);
    </script>
</body>
</html>
